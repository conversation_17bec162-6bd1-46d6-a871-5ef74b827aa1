import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { Separator } from "@/components/ui/separator";
import { footerData } from "@/lib/utils";

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-5">
          {/* Logo and Tagline */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center mb-4">
              <svg width="120" height="35" viewBox="0 0 613 176" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-8 text-black dark:text-white">
                <path d="M529.471 128.71C530.435 130.539 531.477 132.712 533.015 134.115C537.69 138.379 545.42 138.982 551.469 138.625C556.841 138.308 562.384 136.798 565.99 132.541C568.53 129.543 569.57 125.637 569.208 121.753C567.979 108.555 550.873 107.534 540.797 106.222C535.502 105.532 530.166 104.828 524.953 103.653C515.857 101.603 507.009 98.2243 499.853 92.0963C490.718 84.2343 485.126 73.0313 484.332 61.0053C483.332 46.7093 487.967 31.4442 497.428 20.5882C508.922 7.40025 525.006 1.33527 542.097 0.217273C556.098 -0.862727 570.819 2.08927 583.091 8.94527C597.462 16.9733 604.126 29.2513 608.426 44.7043C593.347 45.4263 578.022 44.7323 562.933 44.4123C562.122 43.2873 561.304 42.1452 560.339 41.1442C556.834 37.5042 552.127 36.2663 547.203 36.2203C541.426 36.1683 535.278 37.6883 531.162 41.9613C528.441 44.7863 526.759 48.5053 526.971 52.4713C527.684 65.7533 543.304 66.4742 553.282 67.8552C558.454 68.5712 563.605 69.3163 568.663 70.6463C579.229 73.4253 589.801 78.3182 598.058 85.5632C607.223 93.6052 611.75 104.324 612.439 116.408C613.261 130.823 609.694 144.457 599.897 155.278C586.836 169.704 570.346 174.604 551.425 175.48C537.017 175.722 521.52 173.317 509.102 165.621C495.132 156.964 488.852 144.302 485.163 128.734C499.921 128.444 514.709 128.665 529.471 128.71Z" fill="currentColor"/>
                <path d="M0 173.377L39.135 70.8623C48.066 47.7673 57.33 24.7203 65.811 1.46228L114.915 1.56827C101.935 37.9443 87.377 73.7263 73.865 109.894C86.258 109.702 98.651 109.58 111.045 109.525C119.453 130.888 127.44 152.378 136.259 173.579L86.446 173.509L80.411 158.058L68.034 125.482C61.825 141.487 55.966 157.586 49.54 173.513L0 173.377Z" fill="currentColor"/>
                <path d="M268.231 36.5782L268.343 80.9942C268.404 107.71 269.825 136.433 249.405 156.951C236.752 169.664 220.232 174.481 202.596 174.525C185.191 174.569 164.953 169.98 152.33 157.185C143.747 148.485 137.762 136.134 134.848 124.356C130.156 105.384 131.419 84.1052 131.42 64.6852L131.535 1.55523L178.305 1.46023L178.288 72.0592C178.223 85.3202 176.978 99.1373 178.741 112.301C179.513 118.062 180.888 124.337 184.82 128.813C188.859 133.41 194.634 135.591 200.653 135.872C206.902 136.164 213.166 134.546 217.746 130.109C221.292 126.674 222.825 122.421 223.633 117.662C225.694 105.528 224.513 90.4492 224.51 77.9482L224.582 1.46322C256.723 1.07522 288.91 1.38124 321.054 1.41024L349.016 1.34024C355.16 1.32024 361.532 0.983227 367.632 1.78723C373.933 2.64623 379.98 4.83624 385.37 8.21124C395.914 14.7712 402.731 25.1262 405.508 37.1472C409.113 52.7482 407.593 71.5092 398.896 85.2482C391.142 97.4972 380.962 102.618 367.256 105.737L392.163 147.202C397.442 155.98 402.458 164.985 408.195 173.469L353.825 173.535C348.87 164.073 343.86 154.638 338.797 145.233C335.398 138.668 332.084 131.643 327.722 125.664L327.951 173.542L281.284 173.613C281.48 140.779 281.003 107.874 281.726 75.0522C296.777 74.9312 311.827 74.9312 326.878 75.0532C335.171 75.0822 344.286 75.9432 352.431 74.3852C355.909 73.7192 359.082 72.3332 361.68 69.8822C365.381 66.3922 367.124 61.4642 367.262 56.4502C367.408 51.2062 365.768 46.0112 362.004 42.2592C359.312 39.5762 356.18 38.0032 352.472 37.3062C342.279 35.3902 330.455 36.5802 320.061 36.6332L268.231 36.5782Z" fill="currentColor"/>
                <path d="M422.906 173.561C422.607 133.367 422.553 93.1702 422.743 52.9752C422.743 35.8792 422.289 18.7292 422.746 1.64221C438.358 1.34021 454.018 1.5782 469.636 1.5682C470.073 19.7612 469.498 38.0702 469.501 56.2772L469.521 173.522L422.906 173.561Z" fill="currentColor"/>
              </svg>
            </Link>
            <p className="text-sm text-muted-foreground mb-4">
              Streamlining compliance management for modern businesses with intelligent automation and comprehensive frameworks.
            </p>
          </div>

          {/* Footer Links */}
          {footerData.sections.map((section) => (
            <div key={section.title} className="space-y-3">
              <h4 className="text-sm font-semibold">{section.title}</h4>
              <ul className="space-y-2">
                {section.items.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                      {...(item.external && { target: "_blank", rel: "noopener noreferrer" })}
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* Login Button */}
          <div className="lg:col-span-1 flex flex-col items-start lg:items-end">
            <Button variant="outline" asChild>
              <Link href="/login">Log In</Link>
            </Button>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Bottom Section */}
        <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
          <div className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} Auris Compliance. All rights reserved.
          </div>
          <div className="text-sm text-muted-foreground">
            <Link href="mailto:<EMAIL>" className="hover:text-foreground transition-colors">
              <EMAIL>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
