import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";
import {
  ArrowRight,
  Users,
  Building,
  Shield,
  Zap,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Target,
  Globe,
  Bot,
  Workflow
} from "lucide-react";

export default function SMEsPage() {
  // Testimonials data
  const testimonials = [
    {
      quote: "We operate across three countries with different regulatory requirements. GRCOS gave us centralized control without losing the flexibility to meet local compliance needs. Our audit prep time went from 8 weeks to 2 weeks.",
      name: "<PERSON>",
      designation: "Compliance Director at TechServices Ltd",
      src: "/api/placeholder/400/404"
    },
    {
      quote: "We needed enterprise-level risk management but couldn't justify hiring a full compliance team. GRCOS gives us the capabilities of a much larger organization at a fraction of the cost.",
      name: "<PERSON>",
      designation: "Managing Director at Regional Bank",
      src: "/api/placeholder/400/405"
    },
    {
      quote: "Board meetings are completely different now. Instead of explaining compliance problems, we're discussing strategic opportunities. GRCOS transformed compliance from a cost center to a competitive advantage.",
      name: "<PERSON>",
      designation: "IT Director at Manufacturing Plus",
      src: "/api/placeholder/400/406"
    }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Enterprise Security for Small & Medium Enterprises
            </h1>
            <p className="mt-4 text-xl text-muted-foreground italic">
              Professional compliance management without the enterprise overhead
            </p>
            <h2 className="mt-8 text-2xl font-semibold">
              Mature security operations that match your business ambitions
            </h2>
            <p className="mt-4 text-lg leading-8 text-muted-foreground">
              Get the compliance capabilities and risk management maturity your stakeholders expect—built for SME realities and budgets.
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Your SME operates with enterprise-level complexity but SME-level resources. GRCOS bridges that gap with sophisticated compliance automation that works within your operational constraints and regulatory requirements.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Join Waitlist</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Authority Indicators */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <div className="text-sm text-muted-foreground">Trusted by 300+ regulated SMEs</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <div className="text-sm text-muted-foreground">Supports multi-site, multi-jurisdiction compliance</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <div className="text-sm text-muted-foreground">Average 40% reduction in compliance costs</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <CheckCircle className="h-6 w-6 text-primary" />
              </div>
              <div className="text-sm text-muted-foreground">Built for regulated industries</div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                The SME Compliance Challenge
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                You&apos;re caught between two worlds:
              </p>
              <h3 className="text-2xl font-semibold mb-4">
                Enterprise expectations with SME resources
              </h3>
            </div>

            <div className="mb-12">
              <p className="text-lg text-muted-foreground mb-8">
                Your customers, regulators, and stakeholders expect enterprise-grade security and compliance. But you don&apos;t have enterprise budgets,
                dedicated compliance teams, or the luxury of 18-month implementation projects.
              </p>
            </div>

            <div className="mb-12">
              <h3 className="text-2xl font-semibold mb-8">The specific pressures SMEs face:</h3>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
                    <Shield className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Regulatory scrutiny</h4>
                    <p className="text-muted-foreground">Subject to the same compliance requirements as larger competitors</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900/20">
                    <Users className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Customer due diligence</h4>
                    <p className="text-muted-foreground">B2B clients conduct thorough security assessments</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                    <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Multi-framework complexity</h4>
                    <p className="text-muted-foreground">Often need compliance across 3-5 different standards simultaneously</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                    <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Resource constraints</h4>
                    <p className="text-muted-foreground">Limited dedicated security or compliance personnel</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20">
                    <Globe className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Geographic complexity</h4>
                    <p className="text-muted-foreground">Operating across regions with different regulatory requirements</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
                    <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Audit frequency</h4>
                    <p className="text-muted-foreground">Regular external audits with high stakes for certification maintenance</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg border bg-red-50 dark:bg-red-900/10 p-8">
              <h4 className="text-xl font-semibold mb-4 text-red-800 dark:text-red-200">The cost of inadequate compliance:</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Lost major contracts (average SME loses $5.2M annually to compliance gaps)</li>
                <li>• Regulatory fines and enforcement actions</li>
                <li>• Expensive emergency remediation projects</li>
                <li>• Reputation damage in regulated industries</li>
                <li>• Inability to expand into new markets or regions</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                How GRCOS Solves SME Compliance
              </h2>
              <p className="text-lg text-muted-foreground">
                Enterprise capabilities without enterprise complexity or cost
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">🔄</div>
                  <div>
                    <h3 className="text-xl font-semibold">Multi-framework orchestration</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Manage ISO 27001, SOC 2, PCI DSS, GDPR, and industry-specific requirements from one platform</p>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">⚙️</div>
                  <div>
                    <h3 className="text-xl font-semibold">Mature workflow automation</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Handle complex compliance processes that span departments and locations</p>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">💡</div>
                  <div>
                    <h3 className="text-xl font-semibold">Regulatory intelligence</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Stay ahead of changing requirements across jurisdictions</p>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">👥</div>
                  <div>
                    <h3 className="text-xl font-semibold">Stakeholder management</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Coordinate between internal teams, external auditors, and regulatory bodies</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Modules */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Comprehensive SME Solution Set
              </h2>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">LightHouse: Enterprise Asset & Risk Management</h3>
                  <p className="text-muted-foreground">Complete visibility and control across your organization</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Multi-site asset management: Unified inventory across locations and subsidiaries</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Advanced threat detection: 24/7 monitoring with AI-powered anomaly detection</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Risk correlation: Understand how risks compound across business units</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Compliance mapping: Automatic control testing across multiple frameworks</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">ActionCentre: Mature Process Automation</h3>
                  <p className="text-muted-foreground">Sophisticated workflows that scale across departments</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Cross-functional workflows: Coordinate compliance activities across teams</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Regulatory response automation: Structured responses to regulatory inquiries</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Vendor risk management: Automated third-party risk assessments</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Incident coordination: Enterprise-grade incident response without dedicated CSIRT</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">ComplianceCentre: Multi-Framework Mastery</h3>
                  <p className="text-muted-foreground">Simultaneous management of complex compliance requirements</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Framework harmonization: Optimize overlapping controls across standards</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Regulatory change tracking: Stay current with evolving requirements</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Multi-jurisdiction support: Handle different regulatory requirements by region</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Maturity assessment: Benchmark and improve your compliance program over time</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">TrustCentre: Stakeholder Confidence</h3>
                  <p className="text-muted-foreground">Professional documentation and reporting for all stakeholders</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Audit management: Streamlined coordination with external auditors</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Executive reporting: Board-level risk and compliance dashboards</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Customer assurance: Professional trust portals for due diligence requests</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Regulatory reporting: Automated compliance reporting for various authorities</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Proven SME Success Stories
              </h2>
            </div>

            <div className="space-y-12">
              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Regional Financial Services Firm</h3>
                  <p className="text-lg text-primary italic">125 employees, 4 locations</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">Required SOC 2, PCI DSS, and regional banking regulations compliance</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Approach:</h4>
                    <p className="text-sm text-muted-foreground">Unified multi-framework implementation with centralized monitoring</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Result:</h4>
                    <p className="text-sm text-muted-foreground">Reduced compliance management time by 55%, passed all audits first time, expanded into two new markets</p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Manufacturing Company</h3>
                  <p className="text-lg text-primary italic">200 employees, international operations</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">ISO 27001, GDPR, and country-specific data protection requirements</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Approach:</h4>
                    <p className="text-sm text-muted-foreground">Centralized compliance management with location-specific customization</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Result:</h4>
                    <p className="text-sm text-muted-foreground">Achieved ISO 27001 certification 4 months ahead of schedule, reduced compliance costs by $180K annually</p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Healthcare Services Provider</h3>
                  <p className="text-lg text-primary italic">85 employees, multi-state</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">HIPAA, state healthcare regulations, and SOC 2 for technology services</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Approach:</h4>
                    <p className="text-sm text-muted-foreground">Integrated compliance across clinical and technology operations</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Result:</h4>
                    <p className="text-sm text-muted-foreground">Streamlined compliance across all business units, enabled expansion into 3 new states</p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Professional Services Firm</h3>
                  <p className="text-lg text-primary italic">150 employees, global clients</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">Multiple client-required certifications and international data protection laws</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Approach:</h4>
                    <p className="text-sm text-muted-foreground">Flexible framework management supporting client-specific requirements</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Result:</h4>
                    <p className="text-sm text-muted-foreground">Increased enterprise client base by 40%, reduced proposal response time by 60%</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                SME-Focused Pricing
              </h2>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              <div className="rounded-lg border border-primary bg-primary/5 p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Professional</h3>
                  <div className="text-2xl font-bold text-primary mb-2">$5,999/month</div>
                  <p className="text-sm text-muted-foreground mb-2">For established SMEs with formal compliance requirements</p>
                  <p className="text-xs text-muted-foreground">Designed for organizations that need multiple compliance framework management, multi-location coordination, and professional audit support</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Complete platform access for up to 200 users</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Up to 5 compliance framework implementations</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Advanced workflow automation and orchestration</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Priority support with dedicated customer success manager</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Professional services for initial setup and training</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Enterprise SME</h3>
                  <div className="text-2xl font-bold text-primary mb-2">$9,999/month</div>
                  <p className="text-sm text-muted-foreground mb-2">For complex SMEs with sophisticated requirements</p>
                  <p className="text-xs text-muted-foreground">Perfect for organizations with high-regulation industry requirements and international operations</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Everything in Professional, plus:</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Unlimited framework implementations</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Custom integration development</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Advanced threat intelligence and response capabilities</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">White-glove audit support</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Regulatory change management services</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Regulated Industry Packages</h3>
                  <div className="text-2xl font-bold text-primary mb-2">Custom pricing</div>
                  <p className="text-sm text-muted-foreground">Specialized solutions for healthcare, financial services, government contractors</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Why SMEs Choose GRCOS
              </h2>
              <p className="text-lg text-muted-foreground">
                &ldquo;Finally, compliance management that understands our business model&rdquo;
              </p>
            </div>

            <AnimatedTestimonials testimonials={testimonials} autoplay={true} />
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
              Getting Started
            </h2>
            <p className="text-lg text-muted-foreground mb-12">
              Ready to elevate your compliance program? Most SME implementations show measurable ROI within the first quarter.
            </p>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Assessment & Strategy Session
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">30-Day Pilot Program</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Executive Briefing</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
