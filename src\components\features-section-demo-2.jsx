import { cn } from "@/lib/utils";
import {
  IconAdjustmentsBolt,
  IconCloud,
  IconCurrencyDollar,
  IconEaseInOut,
  IconHeart,
  IconHelp,
  IconRouteAltLeft,
  IconTerminal2,
} from "@tabler/icons-react";

export default function FeaturesSectionDemo() {
  const features = [
    {
      title: "Unified Asset Management",
      description:
        "Blockchain-secured tokenization of IS, OT, and IoT assets with automated discovery and real-time monitoring.",
      icon: <IconTerminal2 />,
    },
    {
      title: "AI-Driven Compliance",
      description:
        "Intelligent policy enforcement and automated compliance assessment across multiple frameworks.",
      icon: <IconEaseInOut />,
    },
    {
      title: "Enterprise-Grade Security",
      description:
        "Military-grade encryption with blockchain verification for audit trails and evidence integrity.",
      icon: <IconCurrencyDollar />,
    },
    {
      title: "Cross-Environment Orchestration",
      description: "Seamless coordination between information systems, operational technology, and IoT devices.",
      icon: <IconCloud />,
    },
    {
      title: "Automated Workflow Management",
      description: "Flowable-powered orchestration with intelligent remediation and incident response automation.",
      icon: <IconRouteAltLeft />,
    },
    {
      title: "Real-time Risk Assessment",
      description:
        "Continuous monitoring with predictive analytics and threat intelligence integration.",
      icon: <IconHelp />,
    },
    {
      title: "Regulatory Framework Support",
      description:
        "OSCAL-standardized compliance management supporting ISO 27001, SOC 2, NIST, and more.",
      icon: <IconAdjustmentsBolt />,
    },
    {
      title: "Interactive Dashboards",
      description: "Comprehensive visualization and reporting through Plotly/Dash with customizable metrics.",
      icon: <IconHeart />,
    },
  ];
  return (
    <div className="relative z-10 py-20 max-w-7xl mx-auto">
      {/* Header and Sub-header */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl font-display text-neutral-800 dark:text-white mb-4">
          The Operating System for GRC
        </h2>
        <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
          Orchestrate Security Compliance Across IS, OT, and IoT environments
        </p>
      </div>
      
      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        {features.map((feature, index) => (
          <Feature key={feature.title} {...feature} index={index} />
        ))}
      </div>
    </div>
  );
}

const Feature = ({
  title,
  description,
  icon,
  index
}) => {
  return (
    <div
      className={cn(
        "flex flex-col lg:border-r  py-10 relative group/feature dark:border-neutral-800",
        (index === 0 || index === 4) && "lg:border-l dark:border-neutral-800",
        index < 4 && "lg:border-b dark:border-neutral-800"
      )}>
      {index < 4 && (
        <div
          className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      {index >= 4 && (
        <div
          className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      <div
        className="mb-4 relative z-10 px-10 text-neutral-600 dark:text-neutral-400">
        {icon}
      </div>
      <div className="text-lg font-bold mb-2 relative z-10 px-10">
        <div
          className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover/feature:bg-blue-500 transition-all duration-200 origin-center" />
        <span
          className="group-hover/feature:translate-x-2 transition duration-200 inline-block text-neutral-800 dark:text-neutral-100">
          {title}
        </span>
      </div>
      <p
        className="text-sm text-neutral-600 dark:text-neutral-300 max-w-xs relative z-10 px-10">
        {description}
      </p>
    </div>
  );
};
