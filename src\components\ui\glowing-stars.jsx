"use client";

import React, { useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import { cn } from "@/lib/utils";
import { useTheme } from "@/components/theme-provider";

export const GlowingStarsBackgroundCard = ({
  className,
  children
}) => {
  const [mouseEnter, setMouseEnter] = useState(false);

  return (
    <div
      onMouseEnter={() => {
        setMouseEnter(true);
      }}
      onMouseLeave={() => {
        setMouseEnter(false);
      }}
      className={cn(
        "bg-[linear-gradient(110deg,#f8f9fa_0.6%,#e9ecef)] dark:bg-[linear-gradient(110deg,#333_0.6%,#222)] p-4 max-w-md w-full min-h-[24rem] rounded-xl border border-neutral-300 dark:border-neutral-600 flex flex-col",
        className
      )}>
      <div className="flex justify-center items-center">
        <Illustration mouseEnter={mouseEnter} />
      </div>
      <div className="px-2 pb-6">{children}</div>
    </div>
  );
};

export const GlowingStarsDescription = ({
  className,
  children
}) => {
  return (
    <p className={cn("text-base text-neutral-700 dark:text-white leading-relaxed", className)}>
      {children}
    </p>
  );
};

export const GlowingStarsTitle = ({
  className,
  children
}) => {
  return (
    <h2 className={cn("font-bold text-2xl text-neutral-800 dark:text-[#eaeaea] leading-tight break-words", className)}>
      {children}
    </h2>
  );
};

export const Illustration = ({
  mouseEnter
}) => {
  const stars = 108;
  const columns = 18;

  const [glowingStars, setGlowingStars] = useState([]);

  const highlightedStars = useRef([]);

  useEffect(() => {
    const interval = setInterval(() => {
      highlightedStars.current = Array.from({ length: 5 }, () =>
        Math.floor(Math.random() * stars));
      setGlowingStars([...highlightedStars.current]);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div
      className="h-48 p-1 w-full"
      style={{
        display: "grid",
        gridTemplateColumns: `repeat(${columns}, 1fr)`,
        gap: `1px`,
      }}>
      {[...Array(stars)].map((_, starIdx) => {
        const isGlowing = glowingStars.includes(starIdx);
        const delay = (starIdx % 10) * 0.1;
        const staticDelay = starIdx * 0.01;
        return (
          <div
            key={`matrix-col-${starIdx}}`}
            className="relative flex items-center justify-center">
            <Star
              isGlowing={mouseEnter ? true : isGlowing}
              delay={mouseEnter ? staticDelay : delay} />
            {mouseEnter && <Glow delay={staticDelay} />}
            <AnimatePresence mode="wait">
              {isGlowing && <Glow delay={delay} />}
            </AnimatePresence>
          </div>
        );
      })}
    </div>
  );
};

const Star = ({
  isGlowing,
  delay
}) => {
  const { theme } = useTheme();
  
  // Theme-aware colors
  const lightGlowColor = "#000000";
  const darkGlowColor = "#ffffff";
  const lightBaseColor = "#999999";
  const darkBaseColor = "#666666";
  
  // Determine if we're in dark mode
  const isDarkMode = theme === "dark" ||
    (theme === "system" && typeof window !== 'undefined' &&
     window.matchMedia("(prefers-color-scheme: dark)").matches);
  
  const glowColor = isDarkMode ? darkGlowColor : lightGlowColor;
  const baseColor = isDarkMode ? darkBaseColor : lightBaseColor;

  return (
    <motion.div
      key={delay}
      initial={{
        scale: 1,
      }}
      animate={{
        scale: isGlowing ? [1, 1.2, 2.5, 2.2, 1.5] : 1,
        background: isGlowing ? glowColor : baseColor,
      }}
      transition={{
        duration: 2,
        ease: "easeInOut",
        delay: delay,
      }}
      className={cn("bg-neutral-400 dark:bg-[#666] h-[1px] w-[1px] rounded-full relative z-20")}></motion.div>
  );
};

const Glow = ({
  delay
}) => {
  return (
    <motion.div
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}
      transition={{
        duration: 2,
        ease: "easeInOut",
        delay: delay,
      }}
      exit={{
        opacity: 0,
      }}
      className="absolute left-1/2 -translate-x-1/2 z-10 h-[4px] w-[4px] rounded-full bg-blue-500 dark:bg-blue-400 blur-[1px] shadow-2xl shadow-blue-400 dark:shadow-blue-300" />
  );
};
