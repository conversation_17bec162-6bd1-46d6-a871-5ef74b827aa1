import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { GlareCard } from "@/components/ui/glare-card";
import { ArrowRight, Shield, CheckCircle, Users, Building, Globe, Lock, Award, Zap, BarChart3, Clock } from "lucide-react";

export default function AllFrameworksPage() {
  const frameworks = [
    {
      name: "ISO 27001",
      descriptor: "Information Security",
      badge: "Certified",
      icon: Shield,
      color: "blue"
    },
    {
      name: "HIPAA",
      descriptor: "Healthcare Privacy",
      badge: "Supported",
      icon: Users,
      color: "green"
    },
    {
      name: "SOC 2",
      descriptor: "Service Controls",
      badge: "Certified",
      icon: Building,
      color: "purple"
    },
    {
      name: "PCI DSS",
      descriptor: "Payment Security",
      badge: "Certified",
      icon: Lock,
      color: "orange"
    },
    {
      name: "GDPR",
      descriptor: "Data Protection",
      badge: "Supported",
      icon: Globe,
      color: "blue"
    },
    {
      name: "NIST 800-82",
      descriptor: "Industrial Control",
      badge: "Supported",
      icon: Shield,
      color: "red"
    },
    {
      name: "CCPA",
      descriptor: "Privacy Rights",
      badge: "Supported",
      icon: Globe,
      color: "indigo"
    },
    {
      name: "FedRAMP",
      descriptor: "Federal Cloud",
      badge: "Supported",
      icon: Building,
      color: "gray"
    },
    {
      name: "ISO 22301",
      descriptor: "Business Continuity",
      badge: "Supported",
      icon: Shield,
      color: "emerald"
    },
    {
      name: "COBIT",
      descriptor: "IT Governance",
      badge: "Supported",
      icon: BarChart3,
      color: "violet"
    },
    {
      name: "NIST CSF",
      descriptor: "Cybersecurity",
      badge: "Certified",
      icon: Shield,
      color: "cyan"
    },
    {
      name: "POPIA",
      descriptor: "Data Protection",
      badge: "Supported",
      icon: Globe,
      color: "teal"
    }
  ];

  const valueProps = [
    {
      icon: Zap,
      title: "One Implementation, Multiple Frameworks",
      description: "Single control implementation automatically satisfies requirements across multiple compliance standards"
    },
    {
      icon: BarChart3,
      title: "Automated Cross-Framework Reporting",
      description: "Generate compliance reports for all frameworks simultaneously with unified evidence collection"
    },
    {
      icon: Clock,
      title: "80% Faster Audit Preparation",
      description: "Streamlined audit processes with pre-mapped controls and automated documentation"
    }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative flex items-center justify-center min-h-[calc(100vh-var(--navbar-height,80px))]">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              One Platform, <span className="text-primary">Multiple Frameworks</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Achieve compliance across all major security and privacy standards with unified control implementation.
              Single implementation satisfies multiple frameworks simultaneously.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Framework Grid */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Supported Compliance Frameworks
              </h2>
              <p className="text-lg text-muted-foreground">
                Professional compliance solutions for all major security and privacy standards
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {frameworks.map((framework) => {
                const IconComponent = framework.icon;
                return (
                  <GlareCard key={framework.name} className="flex flex-col items-center justify-center p-6">
                    <div className="flex flex-col items-center text-center space-y-4">
                      <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                        <IconComponent className="h-6 w-6 text-primary-foreground" />
                      </div>

                      <div className="space-y-2">
                        <h3 className="font-semibold text-lg">{framework.name}</h3>
                        <p className="text-sm text-muted-foreground">{framework.descriptor}</p>
                      </div>

                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        framework.badge === 'Certified'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      }`}>
                        {framework.badge === 'Certified' && <Award className="w-3 h-3 mr-1" />}
                        {framework.badge}
                      </div>
                    </div>
                  </GlareCard>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Value Proposition */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                The Unified Control Advantage
              </h2>
              <p className="text-lg text-muted-foreground">
                Why implement controls separately when one implementation can satisfy multiple frameworks?
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              {valueProps.map((prop, index) => {
                const IconComponent = prop.icon;
                return (
                  <GlareCard key={index} className="p-8">
                    <div className="flex flex-col items-center text-center space-y-4">
                      <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary">
                        <IconComponent className="h-8 w-8 text-primary-foreground" />
                      </div>
                      <h3 className="text-xl font-semibold">{prop.title}</h3>
                      <p className="text-muted-foreground">{prop.description}</p>
                    </div>
                  </GlareCard>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Cross-Framework Visual */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              One Control Implementation = Multiple Framework Requirements
            </h2>
            <div className="bg-background rounded-lg p-8 border">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="text-center">
                  <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-8 w-8 text-primary-foreground" />
                  </div>
                  <h3 className="font-semibold mb-2">Access Control Implementation</h3>
                  <p className="text-sm text-muted-foreground">Single control setup</p>
                </div>
                <div className="flex items-center justify-center">
                  <ArrowRight className="h-8 w-8 text-muted-foreground" />
                </div>
                <div className="text-center">
                  <div className="space-y-2">
                    <div className="flex items-center justify-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">ISO 27001 A.9.1.1</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">SOC 2 CC6.1</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">PCI DSS 7.1</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">NIST CSF PR.AC-1</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 text-primary-foreground">
              See It In Action
            </h2>
            <p className="text-lg text-primary-foreground/80 mb-8">
              Watch how one control implementation automatically satisfies requirements across multiple compliance frameworks.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
