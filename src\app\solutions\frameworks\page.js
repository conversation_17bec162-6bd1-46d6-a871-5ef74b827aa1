import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, Search } from "lucide-react";

export default function AllFrameworksPage() {
  const frameworks = [
    { 
      name: "ISO 27001", 
      href: "/solutions/framework/iso-27001", 
      description: "Information security management compliance",
      category: "Information Security"
    },
    { 
      name: "HIPAA", 
      href: "/solutions/framework/hipaa", 
      description: "Healthcare data protection compliance",
      category: "Healthcare"
    },
    { 
      name: "SOC 2", 
      href: "/solutions/framework/soc-2", 
      description: "Service organization control compliance",
      category: "Service Organizations"
    },
    { 
      name: "PCI DSS", 
      href: "/solutions/framework/pci-dss", 
      description: "Payment card industry data security",
      category: "Financial Services"
    },
    { 
      name: "POPIA", 
      href: "/solutions/framework/popia", 
      description: "South African data protection compliance",
      category: "Data Protection"
    },
    { 
      name: "NIST 800-82", 
      href: "/solutions/framework/nist-800-82", 
      description: "Industrial control systems security guidance",
      category: "Industrial Control Systems"
    },
    { 
      name: "GDPR", 
      href: "/solutions/framework/gdpr", 
      description: "European data protection regulation",
      category: "Data Protection"
    },
    { 
      name: "NIST Cybersecurity Framework", 
      href: "/solutions/framework/nist-csf", 
      description: "Comprehensive cybersecurity framework",
      category: "Cybersecurity"
    },
    { 
      name: "FedRAMP", 
      href: "/solutions/framework/fedramp", 
      description: "Federal cloud security authorization",
      category: "Government"
    },
    { 
      name: "CCPA", 
      href: "/solutions/framework/ccpa", 
      description: "California consumer privacy act compliance",
      category: "Data Protection"
    },
    { 
      name: "ISO 22301", 
      href: "/solutions/framework/iso-22301", 
      description: "Business continuity management",
      category: "Business Continuity"
    },
    { 
      name: "COBIT", 
      href: "/solutions/framework/cobit", 
      description: "IT governance and management framework",
      category: "IT Governance"
    }
  ];

  const categories = [...new Set(frameworks.map(f => f.category))].sort();

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              All Compliance <span className="text-primary">Frameworks</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Comprehensive compliance solutions for all major regulatory frameworks and industry standards. 
              Find the right compliance framework for your organization's needs.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Frameworks Grid */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="mb-12">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
                Supported Frameworks
              </h2>
              <p className="text-lg text-muted-foreground">
                Auris GRCOS supports a comprehensive range of compliance frameworks across industries and regions.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {frameworks.map((framework) => (
                <Link
                  key={framework.name}
                  href={framework.href}
                  className="group rounded-lg border bg-background p-6 hover:shadow-lg transition-all"
                >
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                    <Shield className="h-6 w-6 text-primary-foreground" />
                  </div>
                  <div className="mb-2">
                    <span className="inline-block px-2 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md mb-2">
                      {framework.category}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {framework.name}
                  </h3>
                  <p className="text-muted-foreground mb-4 text-sm">
                    {framework.description}
                  </p>
                  <div className="flex items-center text-primary text-sm font-medium">
                    Learn more <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Framework Categories
            </h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {categories.map((category) => (
                <div key={category} className="rounded-lg border bg-background p-4">
                  <h3 className="font-semibold mb-2">{category}</h3>
                  <p className="text-sm text-muted-foreground">
                    {frameworks.filter(f => f.category === category).length} framework(s)
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Don't See Your Framework?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              We're constantly adding support for new compliance frameworks. Contact us to discuss your specific requirements.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/contact">
                  Contact Us
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/resources/docs">View Documentation</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
