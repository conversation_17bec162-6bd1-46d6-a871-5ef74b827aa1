import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, CheckCircle, FileText, Users, Building, Globe, Lock } from "lucide-react";

export default function AllFrameworksPage() {
  const frameworks = [
    {
      id: "iso-27001",
      name: "ISO 27001",
      description: "Information security management compliance",
      category: "Information Security",
      longDescription: "Streamline your ISO 27001 information security management system implementation and maintenance with Auris GRCOS. Automate controls, manage documentation, and ensure continuous compliance.",
      features: [
        "114 Annex A controls implementation",
        "Risk assessment and treatment",
        "Documentation management",
        "Internal audit scheduling",
        "Certification support"
      ],
      icon: Shield
    },
    {
      id: "hipaa",
      name: "HIPAA",
      description: "Healthcare data protection compliance",
      category: "Healthcare",
      longDescription: "Comprehensive HIPAA compliance solution for healthcare organizations, with automated privacy controls and breach notification management.",
      features: [
        "Administrative safeguards",
        "Physical safeguards",
        "Technical safeguards",
        "Breach notification",
        "Business associate agreements"
      ],
      icon: Users
    },
    {
      id: "soc-2",
      name: "SOC 2",
      description: "Service organization control compliance",
      category: "Service Organizations",
      longDescription: "SOC 2 Type II compliance for service organizations, with automated control testing and continuous monitoring.",
      features: [
        "Trust service criteria",
        "Control environment",
        "Risk assessment",
        "Information and communication",
        "Monitoring activities"
      ],
      icon: Building
    },
    {
      id: "pci-dss",
      name: "PCI DSS",
      description: "Payment card industry data security",
      category: "Financial Services",
      longDescription: "PCI DSS compliance for organizations handling payment card data, with automated vulnerability scanning and network monitoring.",
      features: [
        "Network security",
        "Data protection",
        "Vulnerability management",
        "Access control",
        "Regular monitoring"
      ],
      icon: Lock
    },
    {
      id: "gdpr",
      name: "GDPR",
      description: "European data protection regulation",
      category: "Data Protection",
      longDescription: "Comprehensive GDPR compliance solution for organizations processing EU personal data, with automated privacy controls and data subject rights management.",
      features: [
        "Data processing records",
        "Consent management",
        "Data subject access requests",
        "Privacy by design",
        "Data protection impact assessments"
      ],
      icon: Globe
    },
    {
      id: "nist-800-82",
      name: "NIST 800-82",
      description: "Industrial control systems security guidance",
      category: "Industrial Control Systems",
      longDescription: "NIST 800-82 compliance for industrial control systems, with specialized security controls for operational technology environments.",
      features: [
        "ICS security architecture",
        "Network segmentation",
        "Access control",
        "Incident response",
        "Security monitoring"
      ],
      icon: Shield
    }
  ];

  const categories = [...new Set(frameworks.map(f => f.category))].sort();

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              All Compliance <span className="text-primary">Frameworks</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Comprehensive compliance solutions for all major regulatory frameworks and industry standards.
              Unified platform supporting multiple frameworks with intelligent automation and continuous monitoring.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Join Waitlist</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Framework Navigation */}
      <section className="py-12 bg-muted/50 sticky top-0 z-10">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-6xl">
            <div className="flex flex-wrap justify-center gap-4">
              {frameworks.map((framework) => (
                <Link
                  key={framework.id}
                  href={`#${framework.id}`}
                  className="px-4 py-2 rounded-lg bg-background border hover:bg-primary hover:text-primary-foreground transition-colors text-sm font-medium"
                >
                  {framework.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Framework Sections */}
      {frameworks.map((framework, index) => {
        const IconComponent = framework.icon;
        return (
          <section
            key={framework.id}
            id={framework.id}
            className={`py-20 ${index % 2 === 0 ? 'bg-background' : 'bg-muted/50'}`}
          >
            <div className="container mx-auto px-4">
              <div className="mx-auto max-w-6xl">
                <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 items-center">
                  <div>
                    <div className="flex items-center mb-6">
                      <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mr-4">
                        <IconComponent className="h-8 w-8 text-primary-foreground" />
                      </div>
                      <div>
                        <span className="inline-block px-3 py-1 text-xs font-medium bg-muted text-muted-foreground rounded-md mb-2">
                          {framework.category}
                        </span>
                        <h2 className="text-3xl font-bold tracking-tight">
                          {framework.name}
                        </h2>
                      </div>
                    </div>

                    <p className="text-lg text-muted-foreground mb-8">
                      {framework.longDescription}
                    </p>

                    <div className="flex items-center gap-4 mb-8">
                      <Button size="lg" asChild>
                        <Link href="/demo">
                          See Demo
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" size="lg" asChild>
                        <Link href="/waitlist">Join Waitlist</Link>
                      </Button>
                    </div>
                  </div>

                  <div className="rounded-lg border bg-background p-8">
                    <h3 className="text-xl font-semibold mb-6">Key Features</h3>
                    <ul className="space-y-4">
                      {framework.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start space-x-3">
                          <CheckCircle className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                          <span className="text-muted-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </section>
        );
      })}

      {/* Categories */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Framework Categories
            </h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {categories.map((category) => (
                <div key={category} className="rounded-lg border bg-background p-4">
                  <h3 className="font-semibold mb-2">{category}</h3>
                  <p className="text-sm text-muted-foreground">
                    {frameworks.filter(f => f.category === category).length} framework(s)
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4 text-primary-foreground">
              Ready to Streamline Your Compliance?
            </h2>
            <p className="text-lg text-primary-foreground/80 mb-8">
              See how GRCOS can automate your compliance across multiple frameworks with intelligent orchestration and continuous monitoring.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/waitlist">Join Waitlist</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Frameworks */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              Don't See Your Framework?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              We support 50+ compliance frameworks and are constantly adding more. Contact us to discuss your specific requirements.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/contact">
                  Contact Us
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/resources/docs">View Documentation</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
