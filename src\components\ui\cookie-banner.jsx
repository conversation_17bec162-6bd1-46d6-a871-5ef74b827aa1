"use client";

import React, { useState, useEffect } from "react";
import { ShineBorder } from "@/components/magicui/shine-border";
import { Button } from "@/components/ui/Button";
import { cn } from "@/lib/utils";

export function CookieBanner({
  className,
  isVisible = false,
  onAccept,
  onReject,
  onClose,
  cookiePolicyUrl = "/legal/cookie-policy",
  ...props
}) {

  const handleAccept = () => {
    localStorage.setItem("cookie-choice", "accepted");
    onClose?.();
    onAccept?.();
  };

  const handleReject = () => {
    localStorage.setItem("cookie-choice", "rejected");
    onClose?.();
    onReject?.();
  };

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 z-50 animate-in slide-in-from-bottom-2 duration-300",
        className
      )}
      {...props}
    >
      {/* Card with Shine Border */}
      <div className="relative">
        {/* Shine Border Effect */}
        <ShineBorder
          borderWidth={2}
          duration={12}
          shineColor={["#3b82f6", "#8b5cf6", "#06b6d4"]}
          className="absolute -top-0.5 left-0 right-0 h-0.5 rounded-t-lg"
        />
        
        {/* Card Content */}
        <div className="bg-card border-t-0 border-x border-b border-border shadow-lg backdrop-blur-sm">
          <div className="max-w-4xl mx-auto p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              {/* Content */}
              <div className="flex-1 space-y-2">
                <h2 className="text-lg font-semibold text-card-foreground">
                  Cookie Settings
                </h2>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  We use cookies to improve your experience and for marketing. Visit our{" "}
                  <a
                    href={cookiePolicyUrl}
                    className="inline-flex items-center gap-x-1 text-primary hover:text-primary/80 underline underline-offset-4 font-medium transition-colors"
                  >
                    Cookies Policy
                  </a>{" "}
                  to learn more.
                </p>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-3 shrink-0">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReject}
                  className="min-w-[80px]"
                >
                  Reject
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleAccept}
                  className="min-w-[80px]"
                >
                  Accept
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}