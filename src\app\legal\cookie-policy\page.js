export default function CookiePolicyPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8">Cookie Policy</h1>
        <div className="prose max-w-none">
          <p className="text-lg text-muted-foreground mb-6">
            This is a placeholder page for the Cookie Policy. Content will be added soon.
          </p>
          <p>
            This Cookie Policy explains how Auris Compliance uses cookies and similar technologies to recognize you when you visit our website.
          </p>
          <h2 className="text-2xl font-semibold mt-8 mb-4">What are Cookies</h2>
          <p>Information about what cookies are and how they work will be provided here.</p>
          <h2 className="text-2xl font-semibold mt-8 mb-4">Types of Cookies We Use</h2>
          <p>Details about the different types of cookies we use on our platform.</p>
          <h2 className="text-2xl font-semibold mt-8 mb-4">Managing Cookies</h2>
          <p>Instructions on how users can manage their cookie preferences.</p>
          <h2 className="text-2xl font-semibold mt-8 mb-4">Third-Party Cookies</h2>
          <p>Information about third-party cookies and tracking technologies.</p>
        </div>
      </div>
    </div>
  );
}
