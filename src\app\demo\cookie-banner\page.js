"use client";

import { Button } from "@/components/ui/Button";
import { useCookieBanner } from "@/components/providers/cookie-banner-provider";

export default function CookieBannerDemo() {
  const { showBanner } = useCookieBanner();

  const resetCookieChoice = () => {
    localStorage.removeItem("cookie-choice");
    showBanner();
  };

  const handleAccept = () => {
    console.log("Cookies accepted!");
  };

  const handleReject = () => {
    console.log("Cookies rejected!");
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b border-border">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Cookie Banner Demo
          </h1>
          <p className="text-muted-foreground">
            Demonstration of the cookie banner component with shine border effect.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-12 space-y-8">
        {/* Demo Controls */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-semibold text-card-foreground mb-4">
            Demo Controls
          </h2>
          <div className="space-y-4">
            <p className="text-muted-foreground">
              The cookie banner is triggered by clicking the cookie button in the bottom-right corner.
              Use the buttons below to reset your choice and show the banner again.
            </p>
            <div className="flex gap-3">
              <Button onClick={resetCookieChoice} variant="outline">
                Reset Cookie Choice
              </Button>
              <Button onClick={showBanner} variant="default">
                Show Cookie Banner
              </Button>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-xl font-semibold text-card-foreground mb-4">
            Features
          </h2>
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <h3 className="font-medium text-foreground">✨ Shine Border</h3>
              <p className="text-sm text-muted-foreground">
                Animated gradient border effect at the top of the banner
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-foreground">🎨 Design System</h3>
              <p className="text-sm text-muted-foreground">
                Uses your custom design tokens and color scheme
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-foreground">📱 Responsive</h3>
              <p className="text-sm text-muted-foreground">
                Adapts layout for mobile and desktop screens
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-foreground">💾 Persistent</h3>
              <p className="text-sm text-muted-foreground">
                Remembers user choice in localStorage
              </p>
            </div>
          </div>
        </div>

        {/* Sample Content */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-foreground">
            Sample Page Content
          </h2>
          <div className="space-y-4">
            <p className="text-muted-foreground">
              This is sample content to demonstrate how the cookie banner appears at the bottom
              of the page without interfering with the main content. The banner slides in from
              the bottom with a smooth animation.
            </p>
            <p className="text-muted-foreground">
              The banner includes:
            </p>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground ml-4">
              <li>Clear messaging about cookie usage</li>
              <li>Link to your cookie policy page</li>
              <li>Accept and Reject buttons</li>
              <li>Automatic hiding after user interaction</li>
              <li>Local storage persistence</li>
            </ul>
          </div>
        </div>

        {/* Spacer for demonstration */}
        <div className="h-96 bg-muted/20 rounded-lg flex items-center justify-center">
          <p className="text-muted-foreground">
            Scroll down to see more content and test the banner positioning
          </p>
        </div>

        <div className="h-96 bg-muted/10 rounded-lg flex items-center justify-center">
          <p className="text-muted-foreground">
            The cookie banner stays fixed at the bottom of the viewport
          </p>
        </div>
      </div>
    </div>
  );
}