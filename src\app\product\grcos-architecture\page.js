import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, Database, Zap } from "lucide-react";
import { InteractiveGridPattern } from "@/components/magicui/interactive-grid-pattern";

export default function GRCOSArchitecturePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Interactive Grid Background */}
        <InteractiveGridPattern
          width={60}
          height={60}
          squares={[32, 24]}
          className="absolute inset-0 h-full w-full opacity-50"
          squaresClassName="fill-primary/5 stroke-primary/10 hover:fill-primary/20 transition-all duration-300"
        />
        <div className="container mx-auto px-4 relative z-10">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">GRCOS</span> Architecture
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Discover the core architecture behind our Governance, Risk, and Compliance Operating System. 
              Built for scalability, security, and seamless integration.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Architecture Overview */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Platform Architecture
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Built on modern cloud-native principles for enterprise-grade compliance management.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Shield className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Security First</h3>
              <p className="text-muted-foreground">
                Enterprise-grade security with end-to-end encryption and zero-trust architecture.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Database className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Scalable Infrastructure</h3>
              <p className="text-muted-foreground">
                Cloud-native architecture that scales with your organization's growth and needs.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Zap className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">High Performance</h3>
              <p className="text-muted-foreground">
                Optimized for speed and reliability with 99.9% uptime guarantee.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              More Details Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              We're preparing comprehensive documentation about our GRCOS architecture. 
              Join our waitlist to be notified when it's available.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
