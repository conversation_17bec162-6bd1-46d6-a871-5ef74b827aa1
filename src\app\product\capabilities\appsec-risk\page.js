import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowRight, Code, Shield, Bug, Zap } from "lucide-react";

export default function AppSecRiskPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              AppSec <span className="text-primary">Risk</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Application security risk assessment and management to identify, prioritize, 
              and remediate security vulnerabilities throughout the software development lifecycle.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Key Features
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Code className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Static Code Analysis</h3>
                <p className="text-muted-foreground">
                  Automated static analysis to identify security vulnerabilities in 
                  source code before deployment.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Dynamic Testing</h3>
                <p className="text-muted-foreground">
                  Runtime application security testing to identify vulnerabilities 
                  in running applications.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Bug className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Vulnerability Correlation</h3>
                <p className="text-muted-foreground">
                  Correlate findings from multiple security tools to reduce false 
                  positives and provide comprehensive coverage.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Risk Scoring</h3>
                <p className="text-muted-foreground">
                  Risk-based scoring that considers business context, exploitability, 
                  and potential impact.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Benefits
            </h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Code className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Secure Development</h3>
                  <p className="text-muted-foreground">
                    Integrate security into the development process to catch vulnerabilities early and reduce remediation costs.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Shield className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Comprehensive Coverage</h3>
                  <p className="text-muted-foreground">
                    Cover the entire application portfolio with automated scanning and risk assessment.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Zap className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Faster Remediation</h3>
                  <p className="text-muted-foreground">
                    Accelerate vulnerability remediation with clear prioritization and actionable guidance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
