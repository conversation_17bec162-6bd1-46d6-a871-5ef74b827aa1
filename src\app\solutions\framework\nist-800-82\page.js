import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, Settings, Factory, AlertTriangle } from "lucide-react";

export default function NIST80082Page() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              NIST <span className="text-primary">800-82</span> Compliance
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Comprehensive NIST 800-82 compliance solution for industrial control systems security. 
              Protect critical infrastructure with specialized guidance for ICS/SCADA environments.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Framework Overview */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              NIST 800-82 Framework Overview
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              NIST Special Publication 800-82 provides guidance for establishing secure industrial control systems (ICS). 
              These systems include supervisory control and data acquisition (SCADA) systems, distributed control systems (DCS), 
              and other control system configurations such as programmable logic controllers (PLCs).
            </p>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Factory className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Industrial Control Systems</h3>
                <p className="text-muted-foreground">
                  Specialized security guidance for SCADA, DCS, PLCs, and other industrial control systems 
                  that manage critical infrastructure.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Security Controls</h3>
                <p className="text-muted-foreground">
                  Comprehensive security controls tailored for operational technology (OT) environments 
                  and industrial networks.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Settings className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Risk Management</h3>
                <p className="text-muted-foreground">
                  Risk assessment and management strategies specific to industrial environments 
                  and critical infrastructure protection.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <AlertTriangle className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Incident Response</h3>
                <p className="text-muted-foreground">
                  Incident response procedures designed for industrial control systems 
                  and operational technology environments.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Auris NIST 800-82 Features
            </h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Factory className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">ICS Asset Management</h3>
                  <p className="text-muted-foreground">
                    Comprehensive inventory and management of industrial control system assets including PLCs, HMIs, and SCADA systems.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Shield className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Network Segmentation</h3>
                  <p className="text-muted-foreground">
                    Implementation and monitoring of network segmentation controls to isolate critical industrial systems.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Settings className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Configuration Management</h3>
                  <p className="text-muted-foreground">
                    Secure configuration baselines and change management for industrial control systems and OT networks.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <AlertTriangle className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Vulnerability Management</h3>
                  <p className="text-muted-foreground">
                    Specialized vulnerability assessment and patch management for industrial control systems with minimal operational impact.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Industries We Serve
            </h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Energy & Utilities</h3>
                <p className="text-muted-foreground text-sm">
                  Power generation, transmission, and distribution systems
                </p>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Manufacturing</h3>
                <p className="text-muted-foreground text-sm">
                  Industrial automation and process control systems
                </p>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Water & Wastewater</h3>
                <p className="text-muted-foreground text-sm">
                  Water treatment and distribution control systems
                </p>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Transportation</h3>
                <p className="text-muted-foreground text-sm">
                  Railway, aviation, and traffic control systems
                </p>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Oil & Gas</h3>
                <p className="text-muted-foreground text-sm">
                  Pipeline and refinery control systems
                </p>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">Chemical</h3>
                <p className="text-muted-foreground text-sm">
                  Process control and safety systems
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
