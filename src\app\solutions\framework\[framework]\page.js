import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, CheckCircle, FileText } from "lucide-react";
import { notFound } from "next/navigation";

// Framework data
const frameworkData = {
  "iso-27001": {
    name: "ISO 27001",
    title: "ISO 27001 Information Security Management",
    description: "Comprehensive ISO 27001 compliance solution for information security management systems.",
    longDescription: "Streamline your ISO 27001 information security management system implementation and maintenance with Auris GRCOS. Automate controls, manage documentation, and ensure continuous compliance.",
    features: [
      "114 Annex A controls implementation",
      "Risk assessment and treatment",
      "Documentation management",
      "Internal audit scheduling",
      "Certification support"
    ]
  },
  "hipaa": {
    name: "HIPAA",
    title: "HIPAA Healthcare Compliance",
    description: "Healthcare data protection compliance for medical organizations and health tech companies.",
    longDescription: "Ensure HIPAA compliance for your healthcare organization with automated controls, risk assessments, and comprehensive documentation management.",
    features: [
      "Administrative safeguards",
      "Physical safeguards", 
      "Technical safeguards",
      "Risk assessment tools",
      "Breach notification management"
    ]
  },
  "soc-2": {
    name: "SOC 2",
    title: "SOC 2 Service Organization Controls",
    description: "Service organization control compliance for SaaS and technology companies.",
    longDescription: "Achieve SOC 2 compliance with automated control monitoring, evidence collection, and audit preparation tools designed for modern SaaS companies.",
    features: [
      "Trust service criteria mapping",
      "Control testing automation",
      "Evidence collection",
      "Audit preparation",
      "Continuous monitoring"
    ]
  },
  "pci-dss": {
    name: "PCI DSS",
    title: "PCI DSS Payment Security",
    description: "Payment card industry data security standard compliance for organizations handling card data.",
    longDescription: "Secure payment card data with PCI DSS compliance tools including network security, access controls, and vulnerability management.",
    features: [
      "Network security controls",
      "Access control management",
      "Vulnerability scanning",
      "Security testing",
      "Compliance validation"
    ]
  },
  "popia": {
    name: "POPIA",
    title: "POPIA Data Protection",
    description: "South African Protection of Personal Information Act compliance.",
    longDescription: "Ensure POPIA compliance for your South African operations with comprehensive data protection controls and privacy management tools.",
    features: [
      "Data processing controls",
      "Consent management",
      "Data subject rights",
      "Privacy impact assessments",
      "Breach notification"
    ]
  },
  "gdpr": {
    name: "GDPR",
    title: "GDPR European Data Protection",
    description: "General Data Protection Regulation compliance for European operations.",
    longDescription: "Comprehensive GDPR compliance solution for organizations processing EU personal data, with automated privacy controls and data subject rights management.",
    features: [
      "Data processing records",
      "Consent management",
      "Data subject access requests",
      "Privacy by design",
      "Data protection impact assessments"
    ]
  }
};

export default function FrameworkPage({ params }) {
  const framework = frameworkData[params.framework];
  
  if (!framework) {
    notFound();
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">{framework.name}</span> Compliance Made Simple
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              {framework.longDescription}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              {framework.name} Features
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Everything you need to achieve and maintain {framework.name} compliance.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Shield className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Control Implementation</h3>
              <p className="text-muted-foreground">
                Automated implementation and monitoring of {framework.name} controls with real-time status tracking.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <FileText className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Documentation</h3>
              <p className="text-muted-foreground">
                Centralized management of policies, procedures, and evidence with version control.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <CheckCircle className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Compliance Monitoring</h3>
              <p className="text-muted-foreground">
                Continuous monitoring with automated alerts and corrective action tracking.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features List */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Key Capabilities
            </h2>
          </div>
          
          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {framework.features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-primary mt-1" />
                  <span className="text-muted-foreground">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Detailed Documentation Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Comprehensive {framework.name} implementation guides and feature documentation 
              will be available when we launch.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(frameworkData).map((framework) => ({
    framework: framework,
  }));
}
