import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Building, Shield, Users } from "lucide-react";
import { notFound } from "next/navigation";

// Industry data
const industryData = {
  "financial-services": {
    name: "Financial Services",
    title: "Financial Services Compliance",
    description: "Banking and fintech compliance solutions for financial institutions.",
    longDescription: "Comprehensive compliance solutions designed specifically for banks, credit unions, fintech companies, and other financial institutions. Navigate complex regulatory requirements with confidence.",
    frameworks: ["SOC 2", "PCI DSS", "ISO 27001", "GDPR", "CCPA"],
    challenges: [
      "Complex regulatory landscape",
      "Data security requirements",
      "Customer privacy protection",
      "Audit and reporting demands"
    ]
  },
  "healthcare": {
    name: "Healthcare",
    title: "Healthcare Compliance Solutions",
    description: "Medical and health tech compliance for healthcare organizations.",
    longDescription: "Specialized compliance solutions for hospitals, clinics, health tech companies, and medical device manufacturers. Ensure patient data protection and regulatory compliance.",
    frameworks: ["HIPAA", "ISO 27001", "SOC 2", "FDA 21 CFR Part 11"],
    challenges: [
      "Patient data protection",
      "Medical device regulations",
      "Clinical trial compliance",
      "Healthcare interoperability"
    ]
  },
  "technology": {
    name: "Technology",
    title: "Technology Company Compliance",
    description: "Tech company compliance frameworks for software and SaaS organizations.",
    longDescription: "Tailored compliance solutions for software companies, SaaS providers, and technology startups. Scale your compliance program as you grow.",
    frameworks: ["SOC 2", "ISO 27001", "GDPR", "CCPA", "PCI DSS"],
    challenges: [
      "Rapid scaling requirements",
      "Customer data protection",
      "International compliance",
      "Vendor risk management"
    ]
  },
  "ecommerce": {
    name: "Ecommerce",
    title: "Ecommerce Compliance Management",
    description: "Online retail compliance management for ecommerce businesses.",
    longDescription: "Comprehensive compliance solutions for online retailers, marketplaces, and ecommerce platforms. Protect customer data and ensure payment security.",
    frameworks: ["PCI DSS", "GDPR", "CCPA", "SOC 2", "ISO 27001"],
    challenges: [
      "Payment card security",
      "Customer data privacy",
      "International regulations",
      "Third-party integrations"
    ]
  },
  "education": {
    name: "Education",
    title: "Educational Institution Compliance",
    description: "Educational institution compliance for schools and universities.",
    longDescription: "Specialized compliance solutions for educational institutions, EdTech companies, and student information systems. Protect student data and ensure educational compliance.",
    frameworks: ["FERPA", "COPPA", "SOC 2", "ISO 27001", "GDPR"],
    challenges: [
      "Student data protection",
      "Educational privacy laws",
      "Research data security",
      "Campus security requirements"
    ]
  },
  "manufacturing": {
    name: "Manufacturing",
    title: "Manufacturing Compliance Solutions",
    description: "Industrial compliance requirements for manufacturing organizations.",
    longDescription: "Comprehensive compliance solutions for manufacturing companies, including quality management, environmental compliance, and industrial security requirements.",
    frameworks: ["ISO 9001", "ISO 14001", "ISO 27001", "SOC 2"],
    challenges: [
      "Quality management systems",
      "Environmental compliance",
      "Supply chain security",
      "Industrial IoT security"
    ]
  }
};

export default function IndustryPage({ params }) {
  const industry = industryData[params.industry];
  
  if (!industry) {
    notFound();
  }

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">{industry.name}</span> Compliance Solutions
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              {industry.longDescription}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Supported Frameworks */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Supported Frameworks
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Key compliance frameworks relevant to the {industry.name.toLowerCase()} industry.
            </p>
          </div>
          
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
            {industry.frameworks.map((framework, index) => (
              <div key={index} className="rounded-lg border bg-background p-4 text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mx-auto mb-3">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="font-semibold text-sm">{framework}</h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industry Challenges */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Industry Challenges We Address
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Common compliance challenges faced by {industry.name.toLowerCase()} organizations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {industry.challenges.map((challenge, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  {index + 1}
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{challenge}</h3>
                  <p className="text-muted-foreground">
                    Specialized solutions to address this critical compliance challenge in the {industry.name.toLowerCase()} sector.
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Industry-Specific Features
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Tailored compliance features for {industry.name.toLowerCase()} organizations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Building className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Industry Templates</h3>
              <p className="text-muted-foreground">
                Pre-configured templates and workflows designed specifically for {industry.name.toLowerCase()} compliance.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Shield className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Regulatory Mapping</h3>
              <p className="text-muted-foreground">
                Automated mapping of controls to industry-specific regulations and standards.
              </p>
            </div>
            
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                <Users className="h-8 w-8 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Expert Support</h3>
              <p className="text-muted-foreground">
                Access to compliance experts with deep {industry.name.toLowerCase()} industry knowledge.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Industry Solutions Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Detailed {industry.name.toLowerCase()} compliance solutions and industry-specific features 
              will be available when we launch.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(industryData).map((industry) => ({
    industry: industry,
  }));
}
