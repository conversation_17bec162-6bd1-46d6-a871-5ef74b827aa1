import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Shield,
  CheckCircle,
  Lock,
  Globe,
  Server,
  Database,
  Users,
  Eye,
  FileText,
  AlertTriangle,
  Zap,
  Cloud,
  Key,
  Monitor,
  Activity,
  Settings,
  UserCheck,
  Clock,
  RefreshCw,
  HardDrive,
  Wifi,
  Smartphone
} from "lucide-react";

export default function PlatformCompliancePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Platform <span className="text-primary">Compliance</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Built-in compliance features and controls ensure that Auris GRCOS itself meets
              the highest regulatory and security standards through our secure-by-design architecture.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits of Platform Approach */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Benefits of the Platform Approach to Security
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Secure-by-design architecture based on the shared responsibility model.
            </p>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3 mb-12">
              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                  <Cloud className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-3">AWS Responsibility</h3>
                <p className="text-muted-foreground">
                  AWS is responsible for security of the cloud (global infrastructure).
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                  <Shield className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-3">Auris Responsibility</h3>
                <p className="text-muted-foreground">
                  Auris is responsible for security in the cloud (the SaaS platform).
                </p>
              </div>

              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6">
                  <Users className="h-8 w-8 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-3">Customer Responsibility</h3>
                <p className="text-muted-foreground">
                  Customers are responsible for what goes in the cloud (your data, your users).
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold mb-4">Global availability with regionalized data storage and protections to comply with data privacy and location requirements.</h3>
                <p className="text-muted-foreground mb-4">Supported regions include:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>United States (U.S. East – North Virginia)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Canada (Canada – Central)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Europe (Europe – Frankfurt)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Europe (Europe - London)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Asia Pacific (Asia Pacific – Tokyo)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Asia Pacific (Asia Pacific – Singapore)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Australia (Asia Pacific – Sydney)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>South America (Brazil)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>Africa (South Africa)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>U.S. Federal Customers (AWS GovCloud)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-primary" />
                    <span>U.S. State/Local/Education (AWS GovCloud)</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Unified governance and compliance across the platform.</h3>
                <div className="space-y-2 text-muted-foreground">
                  <p>Auris&apos;s Security Program is based on the NIST Cybersecurity Framework and Auris follows ISO/IEC 27001 standards.</p>
                  <p>The platform is ISO 27001 certified and undergoes annual SSAE 18 SOC 2 Type II audits.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Security Organization */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Security Organization
            </h2>
            <div className="space-y-6 text-muted-foreground">
              <p>
                Auris has a dedicated Security department comprising a diverse team of security professionals
                focusing on product security, security operations, incident response, risk management, and compliance.
              </p>
              <p>
                The Security Team is led by Auris&apos;s Chief Information Security Officer (CISO).
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Shared Responsibility Model */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              The Shared Responsibility Model
            </h2>
            <p className="text-lg text-muted-foreground mb-12">
              Security and compliance is a shared responsibility between Auris and its customers. Auris manages
              the overall application infrastructure and customers manage the end-user security and access control
              to their individual system.
            </p>

            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3 mb-12">
              {/* AWS Responsibility */}
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Cloud className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Amazon Web Services (AWS) Responsibility</h3>
                <p className="text-muted-foreground">
                  Amazon is the largest vendor of data storage and computing on the planet, and they are responsible
                  for the physical facility as well as the physical infrastructure of server hardware, networking,
                  and related services for the Auris One Platform and hosting customer data.
                </p>
              </div>

              {/* Auris Responsibility */}
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Auris Responsibility</h3>
                <p className="text-muted-foreground">
                  In addition to the physical and hardware security that Amazon provides, Auris also has a robust
                  information security environment to ensure that the confidentiality, integrity and availability
                  of customer data meets high standards and customer expectations.
                </p>
              </div>

              {/* Customer Responsibility */}
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Users className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Customer Responsibility</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Customers share the responsibility of not only keeping their data secure, but also complying
                    with applicable regulatory or privacy laws.
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                      <span>Customers have full ownership of their user access controls.</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                      <span>Customers manage their entire data lifecycle.</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                      <span>Customers decide what information goes into the system, how long it should be retained, and what data should be deleted.</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                      <span>Customers determine who can access their data.</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <p className="text-muted-foreground">
                Customers should have controls in place to restrict access to the individuals for whom account
                access is required. Controls should include:
              </p>
              <ul className="space-y-3 text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <UserCheck className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                  <span>approving individuals for access to accounts prior to setting up users in the system.</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Key className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                  <span>revoking users&apos; log-in credentials when user access is no longer required or if user authentication credentials or other sensitive information has been compromised.</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Capabilities */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Platform Capabilities for Customer Responsibilities
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              The platform includes capabilities to assist customers in their responsibility for managing end-user system access:
            </p>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="flex items-start space-x-3">
                <Lock className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Enforce strong passwords</span>
              </div>
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Configure password expiry</span>
              </div>
              <div className="flex items-start space-x-3">
                <Monitor className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Configure session timeout</span>
              </div>
              <div className="flex items-start space-x-3">
                <Key className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Configure SSO (Single-Sign On) via SAML 2.0</span>
              </div>
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Challenge user accounts after multiple failed logins</span>
              </div>
              <div className="flex items-start space-x-3">
                <UserCheck className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Easily delete or suspend user accounts</span>
              </div>
              <div className="flex items-start space-x-3">
                <Globe className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Specifically identify permissible user IP addresses</span>
              </div>
              <div className="flex items-start space-x-3">
                <Activity className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
                <span>Use activity tracking to log access and system use</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Regional Availability and Data Protections */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Regional Availability and Data Protections
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Auris One Platform is available in multiple regions to give customers options for where their data is stored,
              and to enable them to comply with data privacy location requirements.
            </p>

            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">Regional Data Storage</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Data is stored and replicated across state-of-the-art data centers operated by Amazon Web Services (AWS).
                    Upon system setup, your platform data is stored in the data center region associated with the address
                    listed in your Order Form. You may choose an alternative storage region to suit your physical, legal,
                    security, or performance needs.
                  </p>
                  <p>
                    Data is encrypted during transmission and at rest (AES-256) within the regional data storage facility.
                    All platform customer data, including data in backups, are stored exclusively in the single hosting region.
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Data and Service Redundancy</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    All regional equipment is fully redundant and data is replicated or backed-up to alternate regional
                    locations in case of failure.
                  </p>
                  <p>
                    In addition to this real-time redundancy, we back up all customer data, including field data and
                    attached documents that are stored in your account within the system. A full backup of the entire
                    system database is run daily and retained for a one-year period.
                  </p>
                  <p>
                    Backups are kept for the purpose of restoring data integrity due to systemic or database failure,
                    but not for the purpose of restoring user deleted data. As long as your subscription is active,
                    your data will be backed up.
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Data ownership</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Customers own their data completely and are responsible for setting retention spans and for deleting
                    unwanted content during the subscribed service and up to 30 days after termination or expiry of their
                    subscription. Customers have a responsibility of ensuring their data is compliant with applicable
                    policies, regulations, and laws. Auris has the responsibility of ensuring the platform hosting
                    customer data is secure.
                  </p>
                  <p>
                    Customers have several ways (authorized managers or administrators) to extract data at any time.
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Terminating subscriptions</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    When you choose to terminate your subscription, Auris will extend access to the system for an
                    additional 30 days to copy or extract any data you wish to retain. Once you have extracted your data,
                    you have the full ability and responsibility to delete any or all your remaining data in the system.
                  </p>
                  <p>
                    Upon written request, Auris will destroy the customer system and all data content after the extract
                    process. If 90 days has passed without written request to destroy the customer system, Auris reserves
                    the right to destroy the customer system to regain system resources.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Resiliency */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Service Resiliency
            </h2>
            <div className="space-y-6 text-muted-foreground">
              <p>
                Auris is committed to delivering a world-class customer experience. Engineering teams actively monitor
                the platform for availability and performance to a 99.5%+ average uptime.
              </p>
              <p>
                Auris maintains a disaster recovery plan. While the customer impact of a physical or environmental
                threat to its corporate headquarters is considered low (since the vast majority of internal tooling
                is cloud-based), Auris personnel's safety and availability is mission critical.
              </p>
              <p>
                The maximum acceptable length of data loss for the platform (recovery point objective or RPO) is one hour,
                even in the event of disaster. Therefore, backup intervals are configured to allow for loss of customer
                data of one hour or less, depending on the time of system failure.
              </p>
              <p>
                The targeted duration of time and a service level within which service must be restored after a disaster
                (recovery time objective or RTO) is currently 24 hours.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Data Privacy */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Data Privacy
            </h2>
            <div className="space-y-6 text-muted-foreground">
              <p>
                Customer data is considered confidential information and is handled securely by Auris personnel.
                Customer data is never copied to assets outside the production environment, including employee laptops.
              </p>
              <p>
                Any troubleshooting that needs to be performed on customer data is performed in the customer&apos;s environment.
                When Auris personnel need access to a customer environment, a ticket is generated indicating that Support
                accessed the instance, why the interaction was necessary, and what work was performed.
              </p>
              <p>
                Actions by Auris personnel on a customer&apos;s system are limited to resolving the customer needs, and nothing more.
                Once a customer is satisfied with the result, and the ticket is closed, access is removed. Auris collects
                only the minimum personally identifiable information necessary from your licensed users for purposes of
                account set-up, access to product resources, and system administration.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Compliance
            </h2>

            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">Platform Compliance</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Auris follows ISO/IEC 27001 standards to keep information assets secure by implementing an Information
                    Security Management System (ISMS). This provides a systematic approach for managing risk across Auris's
                    staff, processes, and IT systems. Auris&apos;s ISMS is ISO/IEC 27001:2013 certified.
                  </p>
                  <p>
                    Auris One Platform undergoes annual SSAE 18 SOC 2 Type II audits. The SOC 2 Type II audit is an industry
                    recognized, independent audit, which reports on the suitability of the design, and operating effectiveness
                    of Auris&apos;s controls relating to security, availability, and confidentiality.
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Hosting Provider Compliance (AWS)</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Amazon is the largest vendor of data storage and computing on the planet, and they are responsible for
                    the physical facility as well as the physical infrastructure of server hardware, networking, and related
                    services for the Auris One Platform service and hosting customer data.
                  </p>
                  <p>
                    These controls ensure facility and equipment safeguards for areas such as multi-factor access controls,
                    electronic surveillance, intrusion detection systems and environmental safeguards.
                  </p>
                  <p>
                    If you would like to obtain any of the AWS compliance reports, especially their SOC 2, please request
                    instructions from your Auris account executive. Based on the standard agreement all SaaS vendors have
                    with Amazon, Auris cannot provide these reports directly to you. However, your account executive will
                    help you with information to how to obtain reports directly from Amazon.
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Reviewing Auris Policies, Security Documentation, and Audit Reports</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    Robust information security policies and processes are the foundation of Auris One Platform&apos;s security program.
                    Security is reinforced by a range of operational and security policies, standards, and procedures that address
                    various controls and requirements. These measures ensure that our customers can trust the platform to protect
                    their data and maintain the highest levels of confidentiality, integrity, and availability.
                  </p>
                  <p>
                    Upon request and subject to a standard non-disclosure agreement (NDA), customers can obtain a copy of the
                    current platform SOC 2 report. A detailed list of policies is also available upon request.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Security Controls */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Platform Security Controls
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Auris One Platform security is founded on the controls that are built into the service to protect customer data.
              Management regularly assesses risk, monitors the controls, evaluates potential threats, and uses this information
              to update the controls framework from policies and procedures to encryption protocols.
            </p>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="space-y-6">
                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Lock className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Data Encryption</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    Strong encryption is used to protect all data in transit and at rest. Encryption in transit is achieved
                    via the industry-standard TLS (Transport Layer Security) protocol supporting only the strongest encryption
                    algorithms, including AES (Advanced Encryption Standard) with up to 256-bit key lengths.
                  </p>
                </div>

                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Key className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Password Management</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    User passwords are never stored in clear text format. A strong cryptographic algorithm is used to
                    generate irreversible strings known as password hashes.
                  </p>
                </div>

                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <AlertTriangle className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Password Attempts</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    When signing in to our platform or generating a token to use in another application, users have up to
                    five attempts to enter your password. After five attempts, reCAPTCHA displays.
                  </p>
                </div>

                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Clock className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Session Expiry</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    A session is a period of activity between a user logging in and out of an application. Sessions are
                    global to all platform modules. Your session expires if you are inactive for the duration of time
                    set by an Account Admin.
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Shield className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Anti-malware Protections</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    Files uploaded to the platform are scanned for malware to protect users.
                  </p>
                </div>

                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Monitor className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Event Monitoring</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    All product systems are monitored 24/7 for security and availability. In the event of any service
                    interruption, alerts are delivered via e-mail, text message, and phone call to system administrators
                    and management.
                  </p>
                </div>

                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <UserCheck className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Privileged Access</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    Auris follows the principle of least privilege for internal administration. Employees who require
                    administrative access must be requested via a ticketing system. The request requires the approval
                    from management before access is granted.
                  </p>
                </div>

                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <Settings className="h-6 w-6 text-primary" />
                    <h3 className="text-lg font-semibold">Secure Development</h3>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    At all phases in the application development process, security is a top priority. Auris builds
                    security into the platform using secure coding best practices and addressing OWASP Top 10 vulnerabilities.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Incident Management */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Incident Management
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Auris has a robust platform Incident Response Plan to promptly and effectively manage incidents that minimize
              impact to the platform.
            </p>
            <p className="text-muted-foreground mb-8">
              There is a Security Incident Response Team (SIRT) that is responsible for responding, managing, and conducting
              security investigations, including all aspects of communication such as deciding how, when, and to whom the
              findings shall be reported.
            </p>

            <div className="space-y-6">
              <h3 className="text-xl font-semibold mb-6">Incident Response Plan</h3>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="rounded-lg border bg-background p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <Settings className="h-6 w-6 text-primary" />
                    <h4 className="text-lg font-semibold">Preparation</h4>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    Activities that enable the SIRT to respond to an incident: policies, tools, procedures, training,
                    effective governance, and communication plans.
                  </p>
                </div>

                <div className="rounded-lg border bg-background p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <Eye className="h-6 w-6 text-primary" />
                    <h4 className="text-lg font-semibold">Detection & Investigation</h4>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    The discovery of the event with security tools or notification by an inside or outside party about
                    a suspected incident and the declaration and initial classification of the incident.
                  </p>
                </div>

                <div className="rounded-lg border bg-background p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <Shield className="h-6 w-6 text-primary" />
                    <h4 className="text-lg font-semibold">Containment</h4>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    The triage phase where the affected host or system is identified, isolated or otherwise mitigated,
                    and when affected parties are notified and investigative status established.
                  </p>
                </div>

                <div className="rounded-lg border bg-background p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <RefreshCw className="h-6 w-6 text-primary" />
                    <h4 className="text-lg font-semibold">Remediation & Eradication</h4>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    The post-incident repair and recovery of affected systems and or data, communication and instruction
                    to affected parties, and analysis that confirms the threat has been contained.
                  </p>
                </div>

                <div className="rounded-lg border bg-background p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <Activity className="h-6 w-6 text-primary" />
                    <h4 className="text-lg font-semibold">Recovery</h4>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    The analysis of the incident for its procedural and policy implications, the gathering of metrics,
                    and the incorporation of "lessons learned" into future response activities and training.
                  </p>
                </div>

                <div className="rounded-lg border bg-background p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <FileText className="h-6 w-6 text-primary" />
                    <h4 className="text-lg font-semibold">Post-incident Activities</h4>
                  </div>
                  <p className="text-muted-foreground text-sm">
                    Activities within the recovery stage include "Lessons Learned." Lessons Learned allows the SIRT to
                    identify any weaknesses in the plan and put in place remedial actions to mitigate any further such incident.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* AI Usage Principles */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Auris Generative AI Usage Principles
            </h2>

            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">AI Safety and Ethics Task Force</h3>
                <p className="text-muted-foreground">
                  Auris is committed to responsible AI deployment. Auris maintains an AI Safety and Ethics task force
                  comprised of Information Security, Legal, Product, Engineering, and Executive Leadership. This task
                  force meets regularly to actively ensure our use of AI technologies meets the highest ethical and
                  safety standards.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Auris Generative AI Principles</h3>
                <div className="space-y-4 text-muted-foreground">
                  <p>
                    By default, Auris does not use AI models trained on data belonging to customers. Any deviation from
                    this principle requires customer authorization and is always the customer&apos;s choice. Furthermore,
                    Auris products label AI generated content to assist users with identifying AI-generated information.
                  </p>
                  <p>
                    Auris develops AI functionality following the same secure development process as non-AI functionality.
                    Information security controls that protect customer data (e.g. retention, encryption, and residency)
                    are maintained at the same level. Auris products use Amazon Bedrock for hosting and running AI models.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
