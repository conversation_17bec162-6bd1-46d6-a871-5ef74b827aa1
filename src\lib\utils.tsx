import { ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Navigation data structure for mega menus
export const navigationData = {
  product: {
    title: "Product",
    sections: [
      {
        title: "Platform",
        items: [
          { name: "GRCOS Architecture", href: "/product/grcos-architecture", description: "Core platform architecture and infrastructure" },
          { name: "Integrations", href: "/product/integrations", description: "Third-party integrations and APIs" },
          { name: "Platform Compliance", href: "/product/platform-compliance", description: "Built-in compliance features and controls" }
        ]
      },
      {
        title: "Capabilities",
        items: [
          { name: "Exposure Management", href: "/product/capabilities/exposure-management", description: "Comprehensive visibility into your attack surface" },
          { name: "Cyber Risk Quantification", href: "/product/capabilities/cyber-risk-quantification", description: "Quantify and measure cyber risk in business terms" },
          { name: "Cyber Asset Attack Surface Management", href: "/product/capabilities/attack-surface-management", description: "Continuous monitoring and management of attack surfaces" },
          { name: "Risk-Based Vulnerability Management", href: "/product/capabilities/vulnerability-management", description: "Prioritize vulnerabilities based on business risk" },
          { name: "AppSec Risk", href: "/product/capabilities/appsec-risk", description: "Application security risk assessment and management" },
          { name: "Software Bill-of-Materials", href: "/product/capabilities/software-bill-of-materials", description: "Track and manage software components and dependencies" }
        ]
      },
      {
        title: "Modules",
        items: [
          { name: "LightHouse", href: "/product/modules/lighthouse", description: "Centralized data integration management" },
          { name: "ComplianceCentre", href: "/product/modules/compliancecentre", description: "Comprehensive compliance tracking and reporting" },
          { name: "ActionCentre", href: "/product/modules/actioncentre", description: "Task and workflow management system" },
          { name: "TrustCentre", href: "/product/modules/trustcentre", description: "Security and trust transparency portal" }
        ]
      }
    ]
  },
  solutions: {
    title: "Solutions",
    sections: [
      {
        title: "By Framework",
        items: [
          { name: "ISO 27001", href: "/solutions/framework/iso-27001", description: "Information security management compliance" },
          { name: "HIPAA", href: "/solutions/framework/hipaa", description: "Healthcare data protection compliance" },
          { name: "SOC 2", href: "/solutions/framework/soc-2", description: "Service organization control compliance" },
          { name: "PCI DSS", href: "/solutions/framework/pci-dss", description: "Payment card industry data security" },
          { name: "POPIA", href: "/solutions/framework/popia", description: "South African data protection compliance" },
          { name: "NIST 800-82", href: "/solutions/framework/nist-800-82", description: "Industrial control systems security guidance" },
          { name: "All Frameworks", href: "/solutions/frameworks", description: "View all compliance frameworks", featured: true }
        ]
      },
      {
        title: "By Industry",
        items: [
          { name: "Financial Services", href: "/solutions/industry/financial-services", description: "Banking and fintech compliance solutions" },
          { name: "Healthcare", href: "/solutions/industry/healthcare", description: "Medical and health tech compliance" },
          { name: "Manufacturing & Utilities", href: "/solutions/industry/manufacturing-utilities", description: "Industrial and utility compliance management" },
          { name: "Education", href: "/solutions/industry/education", description: "Educational institution compliance" },
          { name: "Technology", href: "/solutions/industry/technology", description: "Tech company compliance frameworks" }
        ]
      },
      {
        title: "By Size",
        items: [
          { name: "Startups", href: "/solutions/size/startups", description: "Early-stage company compliance solutions" },
          { name: "SMBs", href: "/solutions/size/smbs", description: "Small to medium business packages" },
          { name: "Enterprise", href: "/solutions/size/enterprise", description: "Large organization compliance management" }
        ]
      }
    ]
  },
  company: {
    title: "Company",
    sections: [
      {
        title: "About",
        items: [
          { name: "Mission", href: "/company/mission", description: "Our mission and vision for compliance" },
          { name: "Team", href: "/company/team", description: "Meet our leadership and team members" },
          { name: "Partners", href: "/company/partners", description: "Technology and business partnerships" },
          { name: "Careers", href: "/company/careers", description: "Join our growing team" },
          { name: "Auditor Directory", href: "/company/auditor-directory", description: "Certified auditor network" }
        ]
      }
    ]
  },
  demo: {
    title: "Demo",
    sections: [
      {
        title: "See Auris GRCOS in Action",
        items: [
          { name: "Watch Demo", href: "/demo/watch", description: "Product demonstration video", featured: true },
          { name: "Request Demo", href: "/demo/request", description: "Schedule a personalized demo" }
        ]
      }
    ]
  },
  resources: {
    title: "Resources",
    sections: [
      {
        title: "Learn",
        items: [
          { name: "Blog", href: "/resources/blog", description: "Latest insights and articles" },
          { name: "Case Studies", href: "/resources/case-studies", description: "Customer success stories" },
          { name: "Guides", href: "/resources/guides", description: "Implementation guides and best practices" },
          { name: "Docs", href: "/resources/docs", description: "Technical documentation" },
          { name: "FAQ", href: "/resources/faq", description: "Frequently asked questions" },
          { name: "Events & Webinars", href: "/resources/events-webinars", description: "Upcoming and past events" }
        ]
      }
    ]
  },
  follow: {
    title: "Follow",
    sections: [
      {
        title: "Stay Connected",
        items: [
          { name: "Newsletter", href: "/newsletter", description: "Subscribe to our newsletter" },
          { name: "LinkedIn", href: "https://linkedin.com/company/auris-compliance", description: "Follow us on LinkedIn", external: true },
          { name: "Twitter", href: "https://twitter.com/auriscompliance", description: "Follow us on Twitter", external: true },
          { name: "Company Updates", href: "/company/updates", description: "Latest company news and updates" }
        ]
      }
    ]
  }
};

// Footer navigation data
export const footerData = {
  sections: [
    {
      title: "Legal",
      items: [
        { name: "Privacy Policy", href: "/legal/privacy-policy" },
        { name: "Terms of Service", href: "/legal/terms-of-service" },
        { name: "Cookie Policy", href: "/legal/cookie-policy" },
        { name: "Security", href: "/legal/security" }
      ]
    },
    {
      title: "Company",
      items: [
        { name: "Careers", href: "/company/careers" },
        { name: "Partners", href: "/company/partners" },
        { name: "Contact", href: "/contact" }
      ]
    },
    {
      title: "Resources",
      items: [
        { name: "Documentation", href: "/resources/docs" },
        { name: "API", href: "/resources/docs/api" },
        { name: "Support", href: "/contact/support" }
      ]
    },
    {
      title: "Social",
      items: [
        { name: "LinkedIn", href: "https://linkedin.com/company/auris-compliance", external: true },
        { name: "Twitter", href: "https://twitter.com/auriscompliance", external: true }
      ]
    }
  ]
};
