"use client";

import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { useTheme } from "@/components/theme-provider";
import { useCookieBanner } from "@/components/providers/cookie-banner-provider";
import { HoverBorderGradient } from "@/components/ui/hover-border-gradient";
import { useState, useEffect } from "react";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  const { showBanner } = useCookieBanner();
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Monitor scroll position to show/hide scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 200);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleCookieSettings = () => {
    // Show the cookie banner
    showBanner();
  };

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleThemeToggle = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <HoverBorderGradient
        as="div"
        containerClassName="h-12 w-36 shadow-lg"
        className="h-full w-full rounded-full bg-background dark:bg-background flex items-center justify-between px-2 transition-colors"
        duration={2}
      >
        {/* Cookies Management Button */}
        <button
          onClick={handleCookieSettings}
          className="group relative h-8 w-8 rounded-full flex items-center justify-center hover:bg-accent dark:hover:bg-accent transition-colors"
          title="Cookie Settings"
        >
          <Cookie className="h-4 w-4 text-foreground group-hover:text-accent-foreground group-hover:scale-110 transition-all" />
          <span className="sr-only">Cookie settings</span>
        </button>

        {/* Theme Toggle Button */}
        <button
          onClick={handleThemeToggle}
          className="group relative h-8 w-8 rounded-full flex items-center justify-center hover:bg-accent dark:hover:bg-accent transition-colors"
          title="Toggle Theme"
        >
          <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-foreground group-hover:text-accent-foreground group-hover:scale-110" />
          <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-foreground group-hover:text-accent-foreground group-hover:scale-110" />
          <span className="sr-only">Toggle theme</span>
        </button>

        {/* Return to Top Button */}
        <button
          onClick={handleScrollToTop}
          className={`group relative h-8 w-8 rounded-full flex items-center justify-center hover:bg-accent dark:hover:bg-accent transition-all ${
            showScrollTop ? 'opacity-100 scale-100' : 'opacity-50 scale-90'
          }`}
          title="Return to Top"
        >
          <ArrowUp className="h-4 w-4 text-foreground group-hover:text-accent-foreground group-hover:scale-110 group-hover:-translate-y-0.5 transition-all" />
          <span className="sr-only">Return to top</span>
        </button>
      </HoverBorderGradient>
    </div>
  );
}
