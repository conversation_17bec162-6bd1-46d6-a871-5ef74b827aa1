import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import {
  ArrowRight,
  Shield,
  Eye,
  FileText,
  Lock,
  CheckCircle,
  Users,
  BarChart3,
  Clock,
  Database,
  Globe,
  Key,
  Monitor,
  Activity,
  Settings,
  Zap,
  RefreshCw,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Download,
  Search,
  UserCheck,
  Building,
  Smartphone,
  Cloud,
  HardDrive,
  Wifi
} from "lucide-react";

export default function TrustCentrePage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Trust Centre: Building <span className="text-yellow-500">Verifiable Confidence</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              The Evidence That Speaks for Itself
            </p>
            <p className="mt-4 text-base leading-7 text-muted-foreground max-w-3xl mx-auto">
              Trust isn't built on promises—it's built on proof. The GRCOS Trust Centre transforms your compliance evidence from static documents into verifiable, blockchain-secured artifacts that stakeholders can trust without question.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Trust Centre in Action
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Schedule a Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Beyond Documentation: Verifiable Evidence */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Beyond Documentation: <span className="text-yellow-500">Verifiable Evidence</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Your Compliance Story, Cryptographically Secured</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Traditional compliance documentation sits in folders, vulnerable to questions about authenticity and timing. Trust Centre creates an immutable record of your security posture that stands up to the highest scrutiny.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Lock className="h-8 w-8 text-black" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Blockchain-Verified Evidence Repository</h3>
              <p className="text-muted-foreground">
                Every compliance artifact is cryptographically signed and timestamped
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Shield className="h-8 w-8 text-black" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Tamper-Proof Storage</h3>
              <p className="text-muted-foreground">
                Tamper-proof storage ensures evidence integrity from creation to audit
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Clock className="h-8 w-8 text-black" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Point-in-Time Verification</h3>
              <p className="text-muted-foreground">
                Point-in-time verification proves compliance status at any moment in history
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Zap className="h-8 w-8 text-black" />
              </div>
              <h3 className="text-lg font-semibold mb-3">Automated Collection</h3>
              <p className="text-muted-foreground">
                Automated collection eliminates the scramble for evidence during audits
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stakeholder Communication Reimagined */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Stakeholder Communication <span className="text-yellow-500">Reimagined</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">The Right Information, For the Right People, At the Right Time</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Different stakeholders need different views of your security posture. Trust Centre delivers personalized, relevant insights that build confidence across your entire ecosystem.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
            {/* Executive Dashboards */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <BarChart3 className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">👥 Executive Dashboards</h3>
                  <p className="text-sm text-muted-foreground">Strategic Security Visibility</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Transform complex compliance data into executive-ready insights:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <TrendingUp className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk Heat Maps:</strong> Visual representation of your security landscape</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Activity className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Compliance Trends:</strong> Track improvement over time with clear metrics</span>
                </li>
                <li className="flex items-start space-x-3">
                  <BarChart3 className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Investment Impact:</strong> Connect security spending to measurable outcomes</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Globe className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Comparative Benchmarking:</strong> Understand your posture relative to industry peers</span>
                </li>
              </ul>
            </div>

            {/* Board Reporting */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <Building className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">🏛️ Board Reporting</h3>
                  <p className="text-sm text-muted-foreground">Governance-Ready Intelligence</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Generate board-level reports that demonstrate effective security governance:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Regulatory Status Summary:</strong> Clear overview of compliance across all frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <TrendingUp className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk Appetite Alignment:</strong> Show how security decisions align with business strategy</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Users className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Third-Party Assurance:</strong> Evidence of vendor security management</span>
                </li>
                <li className="flex items-start space-x-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Incident Response Readiness:</strong> Demonstrate preparedness without revealing vulnerabilities</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 mt-12">
            {/* Auditor Portals */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <Search className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">🔍 Auditor Portals</h3>
                  <p className="text-sm text-muted-foreground">Streamlined Audit Experience</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Transform audit preparation from months of work to minutes of verification:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <Lock className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Secure Evidence Access:</strong> Controlled, time-limited access to compliance artifacts</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Monitor className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Real-Time Status Updates:</strong> Live compliance dashboard during audit periods</span>
                </li>
                <li className="flex items-start space-x-3">
                  <FileText className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Automated Work Paper Generation:</strong> Pre-formatted evidence packages by framework</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Eye className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Zero-Knowledge Verification:</strong> Prove compliance without exposing sensitive details</span>
                </li>
              </ul>
            </div>

            {/* Customer Trust Centers */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <UserCheck className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">🤝 Customer Trust Centers</h3>
                  <p className="text-sm text-muted-foreground">Transparent Security Communication</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Build customer confidence with transparent, verifiable security information:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <Shield className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Public Security Posture:</strong> Share appropriate security certifications and controls</span>
                </li>
                <li className="flex items-start space-x-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Incident Transparency:</strong> Communicate security events with context and resolution</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Activity className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Continuous Monitoring Status:</strong> Real-time security health indicators</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Lock className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Privacy Compliance Evidence:</strong> Demonstrate data protection commitment</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Audit Facilitation: From Burden to Advantage */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Audit Facilitation: From Burden to <span className="text-yellow-500">Advantage</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Making Audits Work for You</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Traditional audits are disruptive, expensive, and stressful. Trust Centre transforms audits into strategic opportunities to demonstrate security leadership.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Pre-Audit Intelligence */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Search className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Pre-Audit Intelligence</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Automated gap analysis against audit frameworks</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Evidence completeness verification before auditor arrival</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Control effectiveness measurement with supporting data</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Risk assessment alignment with audit scope</span>
                </li>
              </ul>
            </div>

            {/* During-Audit Excellence */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Monitor className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">During-Audit Excellence</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time evidence delivery through secure portals</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Interactive compliance dashboards for auditor review</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Automated sampling and testing support</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Live remediation tracking for any identified gaps</span>
                </li>
              </ul>
            </div>

            {/* Post-Audit Value */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <TrendingUp className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Post-Audit Value</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Blockchain-verified audit trail for future reference</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous monitoring against audit recommendations</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Evidence repository growth for next audit cycle</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Stakeholder communication of audit outcomes</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Reporting Engine */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Advanced Reporting <span className="text-yellow-500">Engine</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Intelligence That Informs Action</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Trust Centre's reporting engine goes beyond static documents to deliver dynamic, actionable intelligence.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Interactive Dashboards */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <BarChart3 className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">📊 Interactive Dashboards</h3>
                  <p className="text-sm text-muted-foreground">Real-Time Security Intelligence</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Built with Plotly/Dash for professional-grade visualization:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <Activity className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Drill-Down Analytics:</strong> Click through from high-level metrics to specific evidence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <TrendingUp className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Time-Series Analysis:</strong> Track security improvements and compliance trends</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Globe className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Cross-Framework Views:</strong> Understand control overlap and coverage gaps</span>
                </li>
                <li className="flex items-start space-x-3">
                  <RefreshCw className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Automated Refresh:</strong> Always current data without manual report generation</span>
                </li>
              </ul>
            </div>

            {/* Executive Reporting */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <Building className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">📈 Executive Reporting</h3>
                  <p className="text-sm text-muted-foreground">Strategic Communication Tools</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Professional reports that communicate security value:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Risk-Based Prioritization:</strong> Focus leadership attention on what matters most</span>
                </li>
                <li className="flex items-start space-x-3">
                  <TrendingUp className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>ROI Calculation:</strong> Demonstrate security investment returns</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Calendar className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Compliance Roadmaps:</strong> Clear timelines for achieving certification goals</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Users className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>Vendor Risk Summaries:</strong> Third-party security posture at a glance</span>
                </li>
              </ul>
            </div>

            {/* Compliance Reports */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex items-center mb-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mr-4">
                  <FileText className="h-6 w-6 text-black" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold">📋 Compliance Reports</h3>
                  <p className="text-sm text-muted-foreground">Framework-Specific Documentation</p>
                </div>
              </div>
              <p className="text-muted-foreground mb-4">
                Generate audit-ready reports for any supported framework:
              </p>
              <ul className="space-y-3 text-sm">
                <li className="flex items-start space-x-3">
                  <Shield className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>NIST CSF 2.0:</strong> Comprehensive cybersecurity posture reports</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Lock className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>ISO 27001:</strong> Statement of Applicability and control evidence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>SOC 2:</strong> Control design and operating effectiveness documentation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Database className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>PCI DSS:</strong> Quarterly scanning and annual assessment support</span>
                </li>
                <li className="flex items-start space-x-3">
                  <UserCheck className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span><strong>HIPAA:</strong> Risk assessment and security rule compliance evidence</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Verification Portal: Stakeholder Self-Service */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Verification Portal: <span className="text-yellow-500">Stakeholder Self-Service</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Secure, Controlled Access to Evidence</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Enable stakeholders to verify your security posture on their timeline while maintaining complete control over access and information sharing.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Multi-Tiered Access Control */}
            <div className="rounded-lg border bg-background p-8">
              <h3 className="text-xl font-semibold mb-6">Multi-Tiered Access Control</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-green-500/20 mt-0.5">
                    <Globe className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Public Tier</h4>
                    <p className="text-sm text-muted-foreground">Basic security certifications and public commitments</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-500/20 mt-0.5">
                    <UserCheck className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Customer Tier</h4>
                    <p className="text-sm text-muted-foreground">Relevant security controls and incident communication</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-purple-500/20 mt-0.5">
                    <Users className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Partner Tier</h4>
                    <p className="text-sm text-muted-foreground">Mutual security assessment and verification capabilities</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-500/20 mt-0.5">
                    <Search className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-sm">Auditor Tier</h4>
                    <p className="text-sm text-muted-foreground">Full evidence access with time and scope controls</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Access Features */}
            <div className="rounded-lg border bg-background p-8">
              <h3 className="text-xl font-semibold mb-6">Access Features</h3>
              <ul className="space-y-4">
                <li className="flex items-start space-x-3">
                  <Clock className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Time-limited portal access with automatic expiration</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Activity className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Audit trail of all verification activities</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Settings className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Granular permission control by stakeholder type</span>
                </li>
                <li className="flex items-start space-x-3">
                  <Download className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Secure document sharing with download tracking</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Evidence Lifecycle Management */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Evidence Lifecycle <span className="text-yellow-500">Management</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">From Creation to Retirement</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Trust Centre manages the complete lifecycle of compliance evidence with automated processes that ensure nothing falls through the cracks.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Automated Collection */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Zap className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Automated Collection</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Integration with all GRCOS modules for seamless evidence gathering</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Scheduled artifact collection aligned with compliance calendars</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time evidence validation and completeness checking</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Automatic metadata tagging for easy retrieval</span>
                </li>
              </ul>
            </div>

            {/* Intelligent Organization */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Database className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Intelligent Organization</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Framework-specific evidence categorization</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Control-based evidence mapping across multiple standards</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Risk-based evidence prioritization for audit focus</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Time-based evidence archival and retention management</span>
                </li>
              </ul>
            </div>

            {/* Continuous Validation */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <RefreshCw className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Continuous Validation</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Blockchain verification of evidence integrity</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Automated freshness checking for time-sensitive evidence</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Control effectiveness validation through continuous monitoring</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Gap identification and remediation tracking</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* The Trust Advantage */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              The <span className="text-yellow-500">Trust Advantage</span>
            </h2>
            <h3 className="text-xl font-semibold mb-6">Why Verifiable Evidence Matters</h3>
            <p className="text-lg text-muted-foreground mb-8">
              In an era of increasing regulatory scrutiny and customer security awareness, the ability to prove your security posture provides significant competitive advantages:
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Faster Sales Cycles */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <TrendingUp className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Faster Sales Cycles</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Immediate verification of security claims during prospect evaluation</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Pre-answered security questionnaires through portal access</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Reduced due diligence friction in enterprise sales</span>
                </li>
              </ul>
            </div>

            {/* Reduced Insurance Costs */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Shield className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Reduced Insurance Costs</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Verifiable security controls for cyber insurance applications</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous monitoring evidence for premium reductions</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Incident response readiness demonstration</span>
                </li>
              </ul>
            </div>

            {/* Audit Efficiency */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <Clock className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Audit Efficiency</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>70% reduction in audit preparation time</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Real-time evidence delivery eliminates information requests</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Continuous compliance reduces surprise findings</span>
                </li>
              </ul>
            </div>

            {/* Stakeholder Confidence */}
            <div className="rounded-lg border bg-background p-8">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-500 mb-6">
                <UserCheck className="h-6 w-6 text-black" />
              </div>
              <h3 className="text-xl font-semibold mb-4">Stakeholder Confidence</h3>
              <ul className="space-y-3 text-sm text-muted-foreground">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Transparent security communication builds customer trust</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Board-ready reporting demonstrates effective governance</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span>Investor confidence through verifiable security management</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Ready to Build Unshakeable Trust? */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Ready to Build <span className="text-yellow-500">Unshakeable Trust?</span>
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Transform your compliance evidence from static documentation into dynamic proof of security compliance.
            </p>
            <div className="flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Trust Centre in Action
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Schedule a Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
