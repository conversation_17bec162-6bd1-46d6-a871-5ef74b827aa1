"use client";

import Link from "next/link";
import { useState } from "react";
import {
  Navbar,
  NavBody,
  MobileNav,
  MobileNavHeader,
  MobileNavMenu,
  MobileNavToggle,
  NavbarButton
} from "@/components/ui/resizable-navbar";
import { MegaMenuNavItems } from "@/components/layout/MegaMenu";
import { navigationData } from "@/lib/utils";

export function Header() {
  const [isOpen, setIsOpen] = useState(false);

  const mobileNavItems = [
    { name: "Product", link: "/product" },
    { name: "Solutions", link: "/solutions" },
    { name: "Company", link: "/company" },
    { name: "Resources", link: "/resources" },
    { name: "Contact", link: "/contact" }
  ];

  return (
    <Navbar className="fixed top-0 left-0 right-0">
      {/* Desktop Navigation */}
      <NavBody>
        {/* Logo */}
        <Link href="/" className="relative z-20 mr-4 flex items-center space-x-2 px-2 py-1 text-sm font-normal text-black">
          <svg width="24" height="30" viewBox="0 0 137 173" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-7 w-auto text-black dark:text-white">
            <path d="M0 171.915L39.135 69.4C48.066 46.305 57.33 23.258 65.811 0L114.915 0.105988C101.935 36.482 87.377 72.264 73.865 108.432C86.258 108.240 98.651 108.118 111.045 108.063C119.453 129.426 127.44 150.916 136.259 172.117L86.446 172.047L80.411 156.596L68.034 124.02C61.825 140.025 55.966 156.124 49.54 172.051L0 171.915Z" fill="currentColor"/>
          </svg>
          <span className="font-display font-semibold text-lg text-black dark:text-white">Auris</span>
        </Link>

        {/* Navigation Items with Mega Menu */}
        <MegaMenuNavItems />

        {/* CTA Buttons */}
        <div className="flex items-center space-x-4">
          <NavbarButton
            href="/demo"
            variant="dark"
            as={Link}
          >
            See Demo
          </NavbarButton>
          <NavbarButton
            href="/waitlist"
            variant="primary"
            as={Link}
          >
            Join Waitlist
          </NavbarButton>
        </div>
      </NavBody>

      {/* Mobile Navigation */}
      <MobileNav>
        <MobileNavHeader>
          {/* Mobile Logo */}
          <Link href="/" className="flex items-center space-x-2 px-2 py-1">
            <svg width="20" height="25" viewBox="0 0 137 173" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-6 w-auto text-black dark:text-white">
              <path d="M0 171.915L39.135 69.4C48.066 46.305 57.33 23.258 65.811 0L114.915 0.105988C101.935 36.482 87.377 72.264 73.865 108.432C86.258 108.240 98.651 108.118 111.045 108.063C119.453 129.426 127.44 150.916 136.259 172.117L86.446 172.047L80.411 156.596L68.034 124.02C61.825 140.025 55.966 156.124 49.54 172.051L0 171.915Z" fill="currentColor"/>
            </svg>
            <span className="font-display font-semibold text-black dark:text-white">Auris</span>
          </Link>

          {/* Mobile Menu Toggle */}
          <MobileNavToggle
            isOpen={isOpen}
            onClick={() => setIsOpen(!isOpen)}
          />
        </MobileNavHeader>

        {/* Mobile Menu */}
        <MobileNavMenu isOpen={isOpen} onClose={() => setIsOpen(false)}>
          <div className="flex flex-col space-y-4 w-full">
            {mobileNavItems.map((item, idx) => (
              <Link
                key={`mobile-${idx}`}
                href={item.link}
                className="text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                {item.name}
              </Link>
            ))}

            <div className="pt-4 border-t space-y-2">
              <NavbarButton
                href="/demo"
                variant="dark"
                as={Link}
                className="w-full"
              >
                See Demo
              </NavbarButton>
              <NavbarButton
                href="/waitlist"
                variant="primary"
                as={Link}
                className="w-full"
              >
                Join Waitlist
              </NavbarButton>
            </div>
          </div>
        </MobileNavMenu>
      </MobileNav>
    </Navbar>
  );
}
