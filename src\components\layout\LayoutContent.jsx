"use client";

import { <PERSON><PERSON> } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { SmoothCursor } from "@/components/ui/smooth-cursor";
import { CookieBanner } from "@/components/ui/cookie-banner";
import { useCookieBanner } from "@/components/providers/cookie-banner-provider";

export function LayoutContent({ children }) {
  const { isVisible, hideBanner } = useCookieBanner();

  return (
    <>
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-24">{children}</main>
        <Footer />
        <ThemeToggle />
      </div>
      <SmoothCursor />
      <CookieBanner 
        isVisible={isVisible}
        onClose={hideBanner}
        cookiePolicyUrl="/legal/cookie-policy" 
      />
    </>
  );
}