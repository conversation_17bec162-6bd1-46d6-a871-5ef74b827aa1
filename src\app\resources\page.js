import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { ArrowRight, BookOpen, FileText, Users, Calendar, HelpCircle, Video } from "lucide-react";

export default function ResourcesPage() {
  const resources = [
    {
      title: "Blog",
      href: "/resources/blog",
      description: "Latest insights, best practices, and industry trends in compliance management.",
      icon: BookOpen,
      featured: true
    },
    {
      title: "Case Studies",
      href: "/resources/case-studies", 
      description: "Real-world success stories from organizations using Auris GRCOS.",
      icon: Users,
      featured: true
    },
    {
      title: "Implementation Guides",
      href: "/resources/guides",
      description: "Step-by-step guides for implementing compliance frameworks.",
      icon: FileText,
      featured: true
    },
    {
      title: "Technical Documentation",
      href: "/resources/docs",
      description: "Comprehensive documentation for platform features and APIs.",
      icon: FileText
    },
    {
      title: "FAQ",
      href: "/resources/faq",
      description: "Frequently asked questions about our platform and compliance.",
      icon: HelpCircle
    },
    {
      title: "Events & Webinars",
      href: "/resources/events-webinars",
      description: "Upcoming events, webinars, and training sessions.",
      icon: Calendar
    }
  ];

  const featuredContent = [
    {
      type: "Guide",
      title: "ISO 27001 Implementation Roadmap",
      description: "A comprehensive guide to implementing ISO 27001 in your organization.",
      href: "/resources/guides/iso-27001-implementation",
      readTime: "15 min read"
    },
    {
      type: "Case Study", 
      title: "How TechCorp Achieved SOC 2 Compliance in 6 Months",
      description: "Learn how a fast-growing SaaS company streamlined their compliance process.",
      href: "/resources/case-studies/techcorp-soc2",
      readTime: "8 min read"
    },
    {
      type: "Blog Post",
      title: "The Future of Compliance Automation",
      description: "Exploring trends and innovations in automated compliance management.",
      href: "/resources/blog/future-compliance-automation",
      readTime: "12 min read"
    }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Compliance <span className="text-primary">Resources</span> & Insights
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Everything you need to succeed with compliance management. From implementation guides 
              to industry insights, we've got you covered.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/resources/guides">
                  Browse Guides
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/resources/blog">View Blog</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Content */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Featured Content
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Our most popular and valuable resources for compliance professionals.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {featuredContent.map((content) => (
              <Link key={content.title} href={content.href} className="group">
                <div className="rounded-lg border bg-background p-6 hover:shadow-lg transition-all">
                  <div className="text-sm font-medium text-primary mb-2">{content.type}</div>
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {content.title}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {content.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{content.readTime}</span>
                    <div className="flex items-center text-primary text-sm font-medium">
                      Read more <ArrowRight className="ml-1 h-4 w-4" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Resource Categories */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Explore Our Resources
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Find the information you need to accelerate your compliance journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {resources.map((resource) => {
              const Icon = resource.icon;
              return (
                <Link
                  key={resource.title}
                  href={resource.href}
                  className={`group rounded-lg border p-6 hover:shadow-lg transition-all ${
                    resource.featured ? 'bg-primary/5 border-primary/20' : 'bg-background'
                  }`}
                >
                  <div className={`flex h-12 w-12 items-center justify-center rounded-lg mb-4 ${
                    resource.featured ? 'bg-primary' : 'bg-muted'
                  }`}>
                    <Icon className={`h-6 w-6 ${
                      resource.featured ? 'text-primary-foreground' : 'text-muted-foreground'
                    }`} />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                    {resource.title}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {resource.description}
                  </p>
                  <div className="flex items-center text-primary text-sm font-medium">
                    Explore <ArrowRight className="ml-1 h-4 w-4" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Stay Updated
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Subscribe to our newsletter for the latest compliance insights, product updates, and industry news.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/newsletter">
                  Subscribe to Newsletter
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/resources/events-webinars">View Events</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Need Personalized Guidance?
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              Our compliance experts are here to help you navigate your specific challenges and requirements.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/contact">
                  Contact Expert
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/demo/request">Book Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
