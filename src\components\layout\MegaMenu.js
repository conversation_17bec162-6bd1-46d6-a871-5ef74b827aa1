"use client";

import Link from "next/link";
import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { navigationData } from "@/lib/utils";

export function MegaMenuNavItems({ className, onItemClick }) {
  const [hovered, setHovered] = useState(null);
  const [megaMenuOpen, setMegaMenuOpen] = useState(null);

  const menuItems = [
    { name: "Product", key: "product", link: "/product" },
    { name: "Solutions", key: "solutions", link: "/solutions" },
    { name: "Company", key: "company", link: "/company" },
    { name: "Resources", key: "resources", link: "/resources" }
  ];

  const handleMouseEnter = (idx, key) => {
    setHovered(idx);
    if (navigationData[key] && navigationData[key].sections) {
      setMegaMenuOpen(key);
    } else {
      setMegaMenuOpen(null);
    }
  };

  const handleMouseLeave = () => {
    setHovered(null);
    setMegaMenuOpen(null);
  };

  return (
    <div className="relative">
      <motion.div
        onMouseLeave={handleMouseLeave}
        className={cn(
          "absolute inset-0 hidden flex-1 flex-row items-center justify-center space-x-6 text-sm font-medium text-zinc-600 transition duration-200 hover:text-zinc-800 lg:flex lg:space-x-6",
          className
        )}
      >
        {menuItems.map((item, idx) => (
          <div
            key={`link-${idx}`}
            className="relative"
            onMouseEnter={() => handleMouseEnter(idx, item.key)}
          >
            <Link
              href={item.link}
              onClick={onItemClick}
              className="relative px-5 py-3 text-neutral-600 dark:text-neutral-300 flex items-center space-x-1"
            >
              {hovered === idx && (
                <motion.div
                  layoutId="hovered"
                  className="absolute inset-0 h-full w-full rounded-full bg-gray-100 dark:bg-neutral-800"
                />
              )}
              <span className="relative z-20">{item.name}</span>
              {navigationData[item.key] && navigationData[item.key].sections && (
                <ChevronDown className="h-3 w-3 relative z-20" />
              )}
            </Link>
          </div>
        ))}
      </motion.div>

      {/* Mega Menu */}
      <AnimatePresence>
        {megaMenuOpen && navigationData[megaMenuOpen] && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 z-50"
            onMouseEnter={() => setMegaMenuOpen(megaMenuOpen)}
            onMouseLeave={handleMouseLeave}
          >
            <div className="bg-white dark:bg-neutral-950 rounded-lg shadow-lg border border-gray-200 dark:border-neutral-800 p-8 px-12 w-max max-w-4xl">
              <MegaMenuContent data={navigationData[megaMenuOpen]} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

function MegaMenuContent({ data }) {
  const getGridCols = () => {
    if (data.sections.length === 1) return "grid-cols-1";
    if (data.sections.length === 2) return "grid-cols-2";
    return "grid-cols-3";
  };

  return (
    <div className={`grid gap-8 lg:gap-12 ${getGridCols()}`}>
      {data.sections.map((section) => (
        <div key={section.title}>
          <h4 className="mb-4 text-sm font-semibold leading-none text-muted-foreground uppercase tracking-wider">
            {section.title}
          </h4>
          <div className="grid gap-2">
            {section.items.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
              >
                <div className="text-sm font-medium leading-relaxed">{item.name}</div>
                {item.description && (
                  <p className="line-clamp-2 text-xs leading-relaxed text-muted-foreground">
                    {item.description}
                  </p>
                )}
              </Link>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
