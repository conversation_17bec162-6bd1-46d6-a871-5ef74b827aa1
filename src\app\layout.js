import { Wix_Madefor_Display, Wix_Madefor_Text } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { CookieBannerProvider } from "@/components/providers/cookie-banner-provider";
import { LayoutContent } from "@/components/layout/LayoutContent";

const wixMadeforDisplay = Wix_Madefor_Display({
  variable: "--font-wix-display",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

const wixMadeforText = Wix_Madefor_Text({
  variable: "--font-wix-text",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

export const metadata = {
  title: "Auris Compliance - Streamline Your Compliance Management",
  description: "Intelligent compliance management platform for modern businesses. Automate frameworks like ISO 27001, HIPAA, SOC 2, and more.",
};


export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${wixMadeforDisplay.variable} ${wixMadeforText.variable} antialiased font-sans`}
      >
        <ThemeProvider defaultTheme="dark" storageKey="auris-ui-theme">
          <CookieBannerProvider>
            <LayoutContent>{children}</LayoutContent>
          </CookieBannerProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
