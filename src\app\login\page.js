export default function LoginPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-md mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center">Log In</h1>
        <div className="bg-card p-8 rounded-lg border">
          <p className="text-lg text-muted-foreground mb-6 text-center">
            This is a placeholder page for Login. Content will be added soon.
          </p>
          <p className="text-center mb-8">
            Sign in to your Auris Compliance account to access your dashboard and manage your compliance workflows.
          </p>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Coming Soon</h3>
              <p className="text-sm text-muted-foreground">
                Our secure login portal will be available here with features including:
              </p>
            </div>
            <ul className="space-y-2 text-sm">
              <li>• Secure user authentication</li>
              <li>• Multi-factor authentication support</li>
              <li>• Single sign-on (SSO) integration</li>
              <li>• Password reset functionality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
