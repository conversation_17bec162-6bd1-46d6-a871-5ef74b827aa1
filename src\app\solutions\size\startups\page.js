import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";
import {
  ArrowRight,
  Users,
  Shield,
  Zap,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Target,
  Lightbulb,
  BarChart3,
  Globe,
  Rocket,
  Bot
} from "lucide-react";

export default function StartupsPage() {
  // Testimonials data
  const testimonials = [
    {
      quote: "We went from zero compliance to SOC 2 ready in 6 weeks. GRCOS made it possible to compete for enterprise contracts without hiring a full security team.",
      name: "<PERSON>",
      designation: "CTO at DataFlow",
      src: "/api/placeholder/400/401"
    },
    {
      quote: "GRCOS helped us demonstrate enterprise-grade security posture during our Series B. The automated reporting and risk management gave investors confidence in our approach.",
      name: "<PERSON>",
      designation: "CEO at FinanceCore",
      src: "/api/placeholder/400/402"
    },
    {
      quote: "What used to take our team weeks of manual work now happens automatically. We can focus on product development while staying compliant across multiple frameworks.",
      name: "<PERSON>",
      designation: "Founder at MedTech Solutions",
      src: "/api/placeholder/400/403"
    }
  ];

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              GRC for <span className="text-primary">Startups</span> & Small Businesses
            </h1>
            <p className="mt-4 text-xl text-muted-foreground">
              Enterprise-grade security without the enterprise complexity
            </p>
            <h2 className="mt-8 text-2xl font-semibold">
              Scale securely from day one
            </h2>
            <p className="mt-4 text-lg leading-8 text-muted-foreground">
              Build trust with customers and investors through automated compliance that grows with your business
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Auris GRCOS delivers enterprise-grade GRC capabilities designed specifically for startups and small businesses.
              Get compliant faster, stay secure longer, and focus on what matters most—growing your business.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo">
                  See Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Join Waitlist</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Stats Bar */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">85%</div>
              <div className="text-sm text-muted-foreground">faster compliance implementation</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">60%</div>
              <div className="text-sm text-muted-foreground">reduction in manual security tasks</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">$200K</div>
              <div className="text-sm text-muted-foreground">average savings on traditional GRC solutions</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">30+</div>
              <div className="text-sm text-muted-foreground">frameworks supported out-of-the-box</div>
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                The Startup Security Dilemma
              </h2>
              <p className="text-xl text-muted-foreground">
                You need enterprise security. You don&apos;t have enterprise resources.
              </p>
            </div>

            <div className="mb-12">
              <p className="text-lg text-muted-foreground mb-8">
                Growing startups face an impossible choice: invest heavily in complex security infrastructure or risk losing customers,
                funding, and competitive advantage. Traditional GRC solutions cost hundreds of thousands of dollars and require
                dedicated security teams you can&apos;t afford to hire.
              </p>
            </div>

            <div>
              <h3 className="text-2xl font-semibold mb-8">Common Challenges:</h3>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 dark:bg-red-900/20">
                    <DollarSign className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Resource constraints</h4>
                    <p className="text-muted-foreground">Limited budget and personnel for security initiatives</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900/20">
                    <Target className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Compliance complexity</h4>
                    <p className="text-muted-foreground">Multiple frameworks with overlapping but different requirements</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                    <TrendingUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Investor requirements</h4>
                    <p className="text-muted-foreground">Due diligence demands robust security posture</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                    <Shield className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Customer trust</h4>
                    <p className="text-muted-foreground">B2B clients require security certifications and compliance proof</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20">
                    <Rocket className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Rapid growth</h4>
                    <p className="text-muted-foreground">Security processes that can&apos;t scale with business expansion</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
                    <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Technical debt</h4>
                    <p className="text-muted-foreground">Security decisions made early that become costly later</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                AI-Powered GRC That Scales With You
              </h2>
              <p className="text-lg text-muted-foreground">
                GRCOS transforms enterprise-grade security into a startup-friendly solution through intelligent automation and unified compliance management.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">🤖</div>
                  <div>
                    <h3 className="text-xl font-semibold">AI-Driven Automation</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Automated risk assessments and remediation planning. Intelligent control mapping across multiple frameworks. Smart workflow orchestration reduces manual effort by 85%</p>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">📊</div>
                  <div>
                    <h3 className="text-xl font-semibold">Unified Compliance Dashboard</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Single view across ISO 27001, SOC 2, PCI DSS, GDPR, and more. Real-time compliance scoring and gap analysis. Automated evidence collection and documentation</p>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">⚡</div>
                  <div>
                    <h3 className="text-xl font-semibold">Rapid Implementation</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Get compliance-ready in weeks, not months. Pre-built templates for common startup scenarios. No dedicated security team required</p>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="flex items-center mb-6">
                  <div className="text-2xl mr-3">💰</div>
                  <div>
                    <h3 className="text-xl font-semibold">Startup-Friendly Pricing</h3>
                  </div>
                </div>
                <p className="text-muted-foreground">Fraction of traditional GRC solution costs. Scale pricing that grows with your business. No hidden fees or surprise charges</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Modules */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Product Modules for Startups
              </h2>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">LightHouse: Your Security Foundation</h3>
                  <p className="text-muted-foreground">Asset Management & Security Monitoring Made Simple</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Asset Discovery: Automatically catalog all your IT assets</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Continuous Monitoring: 24/7 security monitoring without dedicated SOC</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Threat Intelligence: Stay ahead of threats relevant to your industry</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Risk Assessment: AI-powered risk analysis that prioritizes what matters</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">ActionCentre: Automated Response</h3>
                  <p className="text-muted-foreground">Turn Security Events Into Automated Actions</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Incident Response: Pre-built playbooks for common security incidents</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Vulnerability Management: Automated patch management and remediation</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Workflow Automation: Custom workflows that adapt to your processes</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Response Coordination: Streamlined incident response without chaos</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">ComplianceCentre: Framework Mastery</h3>
                  <p className="text-muted-foreground">Navigate Multiple Compliance Requirements Effortlessly</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Multi-Framework Support: ISO 27001, SOC 2, GDPR, PCI DSS, and more</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Gap Analysis: Identify and prioritize compliance gaps</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Policy Management: AI-assisted policy creation and maintenance</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Assessment Automation: Scheduled assessments with minimal manual work</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-primary mb-2">TrustCentre: Evidence & Reporting</h3>
                  <p className="text-muted-foreground">Build Trust Through Transparency</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Automated Documentation: Generate compliance reports automatically</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Stakeholder Portals: Secure sharing with auditors and customers</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Evidence Management: Blockchain-secured compliance artifacts</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-muted-foreground">Audit Readiness: Always ready for customer or regulatory audits</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Use Cases
              </h2>
            </div>

            <div className="space-y-12">
              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Scenario 1: Pre-Series A Startup</h3>
                  <p className="text-lg text-primary italic">&ldquo;We need to show investors we take security seriously&rdquo;</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">12-person SaaS startup preparing for Series A funding round. Investors are asking about security posture and compliance readiness.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Solution:</h4>
                    <p className="text-sm text-muted-foreground">GRCOS provides rapid ISO 27001 and SOC 2 foundation setup, automated risk assessment, and investor-ready security documentation in 4 weeks.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Outcome:</h4>
                    <p className="text-sm text-muted-foreground">Successfully raised $8M Series A with security posture as a competitive advantage.</p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Scenario 2: B2B SaaS Growth Stage</h3>
                  <p className="text-lg text-primary italic">&ldquo;Enterprise customers require security certifications&rdquo;</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">45-person company losing enterprise deals due to lack of security certifications and compliance documentation.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Solution:</h4>
                    <p className="text-sm text-muted-foreground">Accelerated SOC 2 Type II preparation, automated evidence collection, and customer-facing trust portal implementation.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Outcome:</h4>
                    <p className="text-sm text-muted-foreground">Achieved SOC 2 Type II in 6 months, increased enterprise deal closure rate by 300%.</p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Scenario 3: Regulated Industry Startup</h3>
                  <p className="text-lg text-primary italic">&ldquo;We&apos;re in healthcare but can&apos;t afford a full compliance team&rdquo;</p>
                </div>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                    <p className="text-sm text-muted-foreground">Digital health startup needing HIPAA compliance with limited resources and regulatory expertise.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">Solution:</h4>
                    <p className="text-sm text-muted-foreground">HIPAA-specific control implementation, automated risk assessments, and integrated third-party risk management.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">Outcome:</h4>
                    <p className="text-sm text-muted-foreground">Achieved HIPAA compliance in 8 weeks, reduced compliance management time by 75%.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Pricing for Startups
              </h2>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Startup Plan</h3>
                  <div className="text-2xl font-bold text-primary mb-2">$2,500/month</div>
                  <p className="text-sm text-muted-foreground">Perfect for companies with 1-50 employees</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Complete GRCOS platform access</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">2 framework implementations (choose from ISO 27001, SOC 2, GDPR, PCI DSS)</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">AI-powered risk assessments</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Automated workflow orchestration</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border border-primary bg-primary/5 p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Growth Plan</h3>
                  <div className="text-2xl font-bold text-primary mb-2">$5,000/month</div>
                  <p className="text-sm text-muted-foreground">Ideal for scaling companies with 51-200 employees</p>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Everything in Startup, plus:</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">5 framework implementations</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Custom workflow development</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Dedicated customer success manager</span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm">Priority support</span>
                  </li>
                </ul>
              </div>

              <div className="rounded-lg border bg-background p-8">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-2">Custom Enterprise</h3>
                  <div className="text-2xl font-bold text-primary mb-2">Contact us for pricing</div>
                  <p className="text-sm text-muted-foreground">For companies requiring specialized compliance or high-volume processing</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Why Startups Choose GRCOS
              </h2>
              <p className="text-lg text-muted-foreground">
                &ldquo;Finally, enterprise security we can actually afford&rdquo;
              </p>
            </div>

            <AnimatedTestimonials testimonials={testimonials} autoplay={true} />
          </div>
        </div>
      </section>

      {/* Getting Started */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
              Ready to Scale Securely?
            </h2>
            <p className="text-lg text-muted-foreground mb-12">
              Questions? Our startup specialists understand the unique challenges of growing companies. We&apos;re here to help you build security that scales.
            </p>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <Button size="lg" asChild>
                <Link href="/demo">
                  Book a Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/waitlist">Start Free Trial</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
