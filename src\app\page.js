import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { <PERSON><PERSON>Button } from "@/components/magicui/shimmer-button";
import { ShinyButton } from "@/components/magicui/shiny-button";
import { ArrowR<PERSON>, Shield, Zap, Users } from "lucide-react";
import FeaturesSectionDemo from "@/components/features-section-demo-2";
import { GlareCard } from "@/components/ui/glare-card";
import { BackgroundGradientAnimation } from "@/components/ui/background-gradient-animation";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { Spotlight } from "@/components/ui/spotlight-new";
import { GlowingStarsBackgroundCard, GlowingStarsTitle, GlowingStarsDescription } from "@/components/ui/glowing-stars";
import { Ripple } from "@/components/magicui/ripple";
import { cn } from "@/lib/utils";

export default function Home() {

  return (
    <div className="flex flex-col">
      {/* Hero Section with Background Beams */}
      <section className="relative min-h-screen w-full flex flex-col px-4 sm:px-6 lg:px-8 overflow-hidden">
        <BackgroundBeams />
        <div className="hidden dark:block">
          <Spotlight />
        </div>
        <div className="w-full max-w-7xl mx-auto relative z-10 mt-[60px]">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl xl:text-8xl font-display text-neutral-800 dark:text-white mb-6">
              Agentically Automate Security Compliance<br />Across All Environments
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl lg:text-2xl max-w-4xl mx-auto">
              GRCOS unifies governance, risk management, and compliance through blockchain-secured asset management, intelligent policy enforcement, and automated workflow orchestration.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6 flex-wrap">
              <Link href="/waitlist">
                <ShimmerButton>
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </ShimmerButton>
              </Link>
              <Link href="/demo">
                <ShinyButton>See Demo</ShinyButton>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Features */}
      <div className="-mt-40">
        <FeaturesSectionDemo />
      </div>

      {/* GRCOS Modules Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Dot Background */}
        <div
          className={cn(
            "absolute inset-0",
            "[background-size:20px_20px]",
            "[background-image:radial-gradient(#d4d4d4_1px,transparent_1px)]",
            "dark:[background-image:radial-gradient(#404040_1px,transparent_1px)]",
          )}
        />
        {/* Radial gradient for the container to give a faded look */}
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-transparent [mask-image:radial-gradient(ellipse_at_center,transparent_40%,black)]"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl font-display">
              GRCOS Architecture
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Explore the four functional domains that power the GRCOS operating system for GRC.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <Link href="/product/modules/integration-hub" className="block">
              <GlowingStarsBackgroundCard className="cursor-pointer">
                <GlowingStarsTitle>LightHouse</GlowingStarsTitle>
                <GlowingStarsDescription>
                  Automated asset discovery and security monitoring with blockchain tokenization. Real-time surveillance through Wazuh SIEM+XDR.
                </GlowingStarsDescription>
                <div className="flex items-center text-blue-400 mt-4">
                  <span className="text-sm font-medium">Explore Module</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </div>
              </GlowingStarsBackgroundCard>
            </Link>

            <Link href="/product/modules/compliancecentre" className="block">
              <GlowingStarsBackgroundCard className="cursor-pointer">
                <GlowingStarsTitle>ComplianceCentre</GlowingStarsTitle>
                <GlowingStarsDescription>
                  OSCAL-standardized framework management with AI-assisted policy development and cross-framework control mapping.
                </GlowingStarsDescription>
                <div className="flex items-center text-blue-400 mt-4">
                  <span className="text-sm font-medium">Explore Module</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </div>
              </GlowingStarsBackgroundCard>
            </Link>

            <Link href="/product/modules/actioncentre" className="block">
              <GlowingStarsBackgroundCard className="cursor-pointer">
                <GlowingStarsTitle>ActionCentre</GlowingStarsTitle>
                <GlowingStarsDescription>
                  Flowable-powered workflow orchestration with automated remediation management and incident response coordination.
                </GlowingStarsDescription>
                <div className="flex items-center text-blue-400 mt-4">
                  <span className="text-sm font-medium">Explore Module</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </div>
              </GlowingStarsBackgroundCard>
            </Link>

            <Link href="/product/modules/trustcentre" className="block">
              <GlowingStarsBackgroundCard className="cursor-pointer">
                <GlowingStarsTitle>TrustCentre</GlowingStarsTitle>
                <GlowingStarsDescription>
                  Blockchain-verified evidence repository with audit facilitation and interactive dashboards via Plotly/Dash.
                </GlowingStarsDescription>
                <div className="flex items-center text-blue-400 mt-4">
                  <span className="text-sm font-medium">Explore Module</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </div>
              </GlowingStarsBackgroundCard>
            </Link>
          </div>
        </div>
      </section>

      {/* Solutions by Industry */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl font-display">
              Solutions for Every Industry
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Tailored compliance solutions designed for your specific industry needs.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-7xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 place-items-center">
              {[
                {
                  name: "Financial Services",
                  href: "/solutions/industry/financial-services",
                  description: "Comprehensive compliance solutions for banks, fintech, and financial institutions.",
                  icon: "🏦"
                },
                {
                  name: "Healthcare",
                  href: "/solutions/industry/healthcare",
                  description: "HIPAA-compliant solutions for healthcare providers and medical organizations.",
                  icon: "🏥"
                },
                {
                  name: "Technology",
                  href: "/solutions/industry/technology",
                  description: "SOC 2 and ISO 27001 compliance for SaaS and technology companies.",
                  icon: "💻"
                },
                {
                  name: "Manufacturing & Utilities",
                  href: "/solutions/industry/manufacturing-utilities",
                  description: "Industrial and utility compliance management solutions.",
                  icon: "🏭"
                },
                {
                  name: "Education",
                  href: "/solutions/industry/education",
                  description: "FERPA and privacy compliance solutions for educational institutions.",
                  icon: "🎓"
                }
              ].map((industry) => (
                <Link key={industry.name} href={industry.href} className="block">
                  <GlareCard className="cursor-pointer">
                    <div className="p-6 h-full flex items-center justify-center gap-6">
                      <div className="flex-shrink-0">
                        <div className="text-5xl">{industry.icon}</div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-neutral-800 dark:text-white mb-2">
                          {industry.name}
                        </h3>
                        <p className="text-sm text-neutral-600 dark:text-gray-300 leading-relaxed mb-3">
                          {industry.description}
                        </p>
                        <div className="flex items-center text-primary group-hover:text-primary-foreground transition-colors">
                          <span className="text-sm font-medium">Learn More</span>
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </GlareCard>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-20 overflow-hidden bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20">
        <Ripple
          mainCircleSize={210}
          mainCircleOpacity={0.24}
          numCircles={8}
          className="opacity-60"
        />
        <div className="container mx-auto px-4 relative z-10">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-neutral-800 dark:text-white sm:text-4xl font-display">
              Ready for Enterprise-Grade GRC?
            </h2>
            <p className="mt-4 text-lg text-neutral-600 dark:text-neutral-300">
              GRCOS democratizes enterprise-grade security operations for SMBs with blockchain-secured trust, AI-driven automation, and cryptographic verification.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Link href="/waitlist">
                <ShimmerButton>
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </ShimmerButton>
              </Link>
              <Link href="/demo">
                <ShinyButton>See Demo</ShinyButton>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
