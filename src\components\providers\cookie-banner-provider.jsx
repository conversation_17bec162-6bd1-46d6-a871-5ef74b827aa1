"use client";

import React, { createContext, useContext, useState } from "react";

const CookieBannerContext = createContext();

export function CookieBannerProvider({ children }) {
  const [isVisible, setIsVisible] = useState(false);

  const showBanner = () => {
    setIsVisible(true);
  };

  const hideBanner = () => {
    setIsVisible(false);
  };

  const value = {
    isVisible,
    showBanner,
    hideBanner,
  };

  return (
    <CookieBannerContext.Provider value={value}>
      {children}
    </CookieBannerContext.Provider>
  );
}

export function useCookieBanner() {
  const context = useContext(CookieBannerContext);
  if (!context) {
    throw new Error("useCookieBanner must be used within a CookieBannerProvider");
  }
  return context;
}