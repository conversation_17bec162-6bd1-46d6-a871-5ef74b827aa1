"use client";

import React, { useEffect, useRef, useState } from "react";
import { AnimatePresence, motion } from "motion/react";
import { cn } from "@/lib/utils";
import { useTheme } from "@/components/theme-provider";

export const GlowingStarBackground = ({
  children,
  className,
  containerClassName,
  stars = 200,
  columns = 25,
  glowFrequency = 3000,
  ...props
}) => {
  const [glowingStars, setGlowingStars] = useState([]);
  const [mouseEnter, setMouseEnter] = useState(false);
  const highlightedStars = useRef([]);

  useEffect(() => {
    const interval = setInterval(() => {
      highlightedStars.current = Array.from({ length: 8 }, () =>
        Math.floor(Math.random() * stars)
      );
      setGlowingStars([...highlightedStars.current]);
    }, glowFrequency);

    return () => clearInterval(interval);
  }, [stars, glowFrequency]);

  return (
    <div
      className={cn(
        "relative flex h-screen w-full items-center justify-center bg-background overflow-hidden",
        containerClassName
      )}
      onMouseEnter={() => setMouseEnter(true)}
      onMouseLeave={() => setMouseEnter(false)}
    >
      {/* Stars Grid */}
      <div
        className="absolute inset-0 w-full h-full"
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(${columns}, 1fr)`,
          gap: "2px",
        }}
      >
        {[...Array(stars)].map((_, starIdx) => {
          const isGlowing = glowingStars.includes(starIdx);
          const delay = (starIdx % 20) * 0.05;
          const staticDelay = starIdx * 0.005;
          
          return (
            <div
              key={`star-${starIdx}`}
              className="relative flex items-center justify-center"
            >
              <Star
                isGlowing={mouseEnter ? true : isGlowing}
                delay={mouseEnter ? staticDelay : delay}
              />
              {mouseEnter && <Glow delay={staticDelay} />}
              <AnimatePresence mode="wait">
                {isGlowing && <Glow delay={delay} />}
              </AnimatePresence>
            </div>
          );
        })}
      </div>
      
      {/* Radial gradient overlay for faded look */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-background/30 [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
      
      {/* Content */}
      <div className={cn("relative z-20", className)} {...props}>
        {children}
      </div>
    </div>
  );
};

const Star = ({ isGlowing, delay }) => {
  const { theme } = useTheme();
  
  // Theme-aware colors
  const lightGlowColor = "#000000";
  const darkGlowColor = "#ffffff";
  const lightBaseColor = "#999999";
  const darkBaseColor = "#666666";
  
  // Determine if we're in dark mode
  const isDarkMode = theme === "dark" ||
    (theme === "system" && typeof window !== 'undefined' &&
     window.matchMedia("(prefers-color-scheme: dark)").matches);
  
  const glowColor = isDarkMode ? darkGlowColor : lightGlowColor;
  const baseColor = isDarkMode ? darkBaseColor : lightBaseColor;

  return (
    <motion.div
      key={delay}
      initial={{
        scale: 1,
      }}
      animate={{
        scale: isGlowing ? [1, 1.2, 2.5, 2.2, 1.5] : 1,
        background: isGlowing ? glowColor : baseColor,
      }}
      transition={{
        duration: 2,
        ease: "easeInOut",
        delay: delay,
      }}
      className={cn("bg-neutral-400 dark:bg-[#666] h-[1px] w-[1px] rounded-full relative z-20")}
    />
  );
};

const Glow = ({ delay }) => {
  return (
    <motion.div
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}
      transition={{
        duration: 2,
        ease: "easeInOut",
        delay: delay,
      }}
      exit={{
        opacity: 0,
      }}
      className="absolute left-1/2 -translate-x-1/2 z-10 h-[4px] w-[4px] rounded-full bg-blue-500 dark:bg-blue-400 blur-[1px] shadow-2xl shadow-blue-400 dark:shadow-blue-300"
    />
  );
};

export function GlowingStarBackgroundDemo() {
  return (
    <GlowingStarBackground>
      <p className="relative z-20 bg-gradient-to-b from-neutral-200 to-neutral-500 bg-clip-text py-8 text-4xl font-bold text-transparent sm:text-7xl">
        Glowing Stars
      </p>
    </GlowingStarBackground>
  );
}

// Keep the old DotBackground for backward compatibility
export const DotBackground = ({
  children,
  className,
  containerClassName,
  dotSize = "20px",
  dotColor,
  ...props
}) => {
  return (
    <div
      className={cn(
        "relative flex h-screen w-full items-center justify-center bg-background",
        containerClassName
      )}
    >
      <div
        className={cn(
          "absolute inset-0",
          `[background-size:${dotSize}_${dotSize}]`,
          dotColor
            ? `[background-image:radial-gradient(${dotColor}_1px,transparent_1px)]`
            : "[background-image:radial-gradient(#d4d4d4_1px,transparent_1px)] dark:[background-image:radial-gradient(#404040_1px,transparent_1px)]"
        )}
      />
      {/* Radial gradient for the container to give a faded look */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-background [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]"></div>
      <div className={cn("relative z-20", className)} {...props}>
        {children}
      </div>
    </div>
  );
};

export function DotBackgroundDemo() {
  return (
    <DotBackground>
      <p className="relative z-20 bg-gradient-to-b from-neutral-200 to-neutral-500 bg-clip-text py-8 text-4xl font-bold text-transparent sm:text-7xl">
        Backgrounds
      </p>
    </DotBackground>
  );
}