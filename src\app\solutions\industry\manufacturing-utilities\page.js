import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Factory, Zap, Shield, Settings, AlertTriangle, Users } from "lucide-react";

export default function ManufacturingUtilitiesPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Manufacturing & <span className="text-primary">Utilities</span> Compliance
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Specialized compliance solutions for manufacturing and utility companies. 
              Protect critical infrastructure and industrial operations with comprehensive security frameworks.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Industry Challenges */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Industry-Specific Challenges
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Factory className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Operational Technology Security</h3>
                <p className="text-muted-foreground">
                  Securing industrial control systems, SCADA networks, and manufacturing equipment 
                  while maintaining operational continuity and safety.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Critical Infrastructure Protection</h3>
                <p className="text-muted-foreground">
                  Protecting essential services and infrastructure from cyber threats while 
                  ensuring regulatory compliance and business continuity.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Legacy System Integration</h3>
                <p className="text-muted-foreground">
                  Integrating modern security controls with legacy industrial systems 
                  and aging infrastructure without disrupting operations.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <AlertTriangle className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Safety & Security Convergence</h3>
                <p className="text-muted-foreground">
                  Balancing cybersecurity requirements with operational safety protocols 
                  and ensuring both physical and digital security.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Relevant Frameworks */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Relevant Compliance Frameworks
            </h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Shield className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">NIST 800-82 (ICS Security)</h3>
                  <p className="text-muted-foreground">
                    Specialized guidance for securing industrial control systems, SCADA, and operational technology environments.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Settings className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">ISO 27001</h3>
                  <p className="text-muted-foreground">
                    Information security management system framework applicable to both IT and OT environments.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Factory className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">NERC CIP (Electric Utilities)</h3>
                  <p className="text-muted-foreground">
                    Critical infrastructure protection standards for electric utility companies and power grid operators.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Zap className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">TSA Pipeline Security</h3>
                  <p className="text-muted-foreground">
                    Transportation Security Administration guidelines for pipeline and energy infrastructure security.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Our Solutions
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Asset Discovery & Management</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Comprehensive OT asset inventory</li>
                  <li>• Network topology mapping</li>
                  <li>• Device configuration management</li>
                  <li>• Change detection and monitoring</li>
                </ul>
              </div>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Risk Assessment & Management</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• OT-specific risk assessments</li>
                  <li>• Vulnerability management for ICS</li>
                  <li>• Threat intelligence integration</li>
                  <li>• Business impact analysis</li>
                </ul>
              </div>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Network Security</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Network segmentation design</li>
                  <li>• Industrial firewall management</li>
                  <li>• Secure remote access</li>
                  <li>• Network monitoring and detection</li>
                </ul>
              </div>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold">Compliance Management</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Automated compliance reporting</li>
                  <li>• Audit preparation and support</li>
                  <li>• Policy and procedure management</li>
                  <li>• Continuous compliance monitoring</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries Served */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Industries We Serve
            </h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mx-auto mb-4">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Energy & Power</h3>
                <p className="text-muted-foreground text-sm">
                  Electric utilities, renewable energy, and power generation
                </p>
              </div>

              <div className="text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mx-auto mb-4">
                  <Factory className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Manufacturing</h3>
                <p className="text-muted-foreground text-sm">
                  Automotive, aerospace, chemicals, and industrial production
                </p>
              </div>

              <div className="text-center">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mx-auto mb-4">
                  <Settings className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Water & Wastewater</h3>
                <p className="text-muted-foreground text-sm">
                  Water treatment, distribution, and wastewater management
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
