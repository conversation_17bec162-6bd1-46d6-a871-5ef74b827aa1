import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { ArrowR<PERSON>, Users, Linkedin } from "lucide-react";

export default function TeamPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Meet Our <span className="text-primary">Team</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              The passionate experts behind Auris Compliance, dedicated to transforming 
              how organizations approach governance, risk, and compliance management.
            </p>
          </div>
        </div>
      </section>

      {/* Team Introduction */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Building the Future of Compliance
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Our team combines deep expertise in compliance, technology, and business operations 
              to create solutions that truly understand the challenges organizations face. 
              We're a diverse group of professionals united by our mission to make compliance 
              accessible and automated for everyone.
            </p>
          </div>
        </div>
      </section>

      {/* Team Stats */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-4 text-center">
              <div>
                <div className="text-4xl font-bold text-primary mb-2">15+</div>
                <div className="text-muted-foreground">Team Members</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">50+</div>
                <div className="text-muted-foreground">Years Combined Experience</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">10+</div>
                <div className="text-muted-foreground">Compliance Frameworks</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">5</div>
                <div className="text-muted-foreground">Countries Represented</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Placeholder */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Leadership Team
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Meet the leaders driving our mission forward.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {[1, 2, 3].map((member) => (
              <div key={member} className="text-center">
                <div className="mx-auto h-32 w-32 rounded-full bg-muted mb-6 flex items-center justify-center">
                  <Users className="h-16 w-16 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">Team Member {member}</h3>
                <p className="text-muted-foreground mb-4">Leadership Role</p>
                <Button variant="outline" size="sm">
                  <Linkedin className="h-4 w-4 mr-2" />
                  LinkedIn
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Values */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              What Drives Us
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              The values and principles that guide our team every day.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Customer Success</h3>
              <p className="text-muted-foreground">
                We measure our success by the success of our customers. Every decision 
                we make is guided by how it will impact the organizations we serve.
              </p>
            </div>
            
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Continuous Learning</h3>
              <p className="text-muted-foreground">
                The compliance landscape is constantly evolving, and so are we. 
                We're committed to staying at the forefront of regulatory changes and technology.
              </p>
            </div>
            
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Collaboration</h3>
              <p className="text-muted-foreground">
                We believe the best solutions come from diverse perspectives and collaborative effort. 
                Our team works together to solve complex challenges.
              </p>
            </div>
            
            <div className="p-6 rounded-lg border">
              <h3 className="text-lg font-semibold mb-3">Innovation</h3>
              <p className="text-muted-foreground">
                We're not afraid to challenge the status quo and find new ways to solve 
                old problems in the compliance industry.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Join Team CTA */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Join Our Growing Team
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              We're always looking for passionate individuals who want to make a difference 
              in the compliance industry. Explore our open positions.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/company/careers">
                  View Open Positions
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Us</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
