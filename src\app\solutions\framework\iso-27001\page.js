import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Shield, CheckCircle, FileText, Users, BarChart3 } from "lucide-react";

export default function ISO27001Page() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">ISO 27001</span> Compliance Made Simple
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Streamline your ISO 27001 information security management system implementation and maintenance 
              with Auris GRCOS. Automate controls, manage documentation, and ensure continuous compliance.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/demo/request">
                  Book ISO 27001 Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/contact">Speak with Expert</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              ISO 27001 Features
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Everything you need to achieve and maintain ISO 27001 certification.
            </p>
          </div>
          
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Shield className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Control Implementation</h3>
              <p className="text-muted-foreground">
                Automated implementation and monitoring of all 114 ISO 27001 Annex A controls with real-time status tracking.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <FileText className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Documentation Management</h3>
              <p className="text-muted-foreground">
                Centralized management of policies, procedures, and evidence with version control and approval workflows.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <BarChart3 className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Risk Assessment</h3>
              <p className="text-muted-foreground">
                Comprehensive risk assessment tools with automated risk calculations and treatment planning.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <Users className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Audit Management</h3>
              <p className="text-muted-foreground">
                Internal audit scheduling, execution, and tracking with automated evidence collection and reporting.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <CheckCircle className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Compliance Monitoring</h3>
              <p className="text-muted-foreground">
                Continuous monitoring of compliance status with automated alerts for non-conformities and corrective actions.
              </p>
            </div>

            <div className="rounded-lg border bg-background p-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                <FileText className="h-6 w-6 text-primary-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Certification Support</h3>
              <p className="text-muted-foreground">
                Certification readiness assessments and support throughout the external audit process.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Implementation Process
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Our structured approach to ISO 27001 implementation ensures success.
            </p>
          </div>
          
          <div className="mx-auto max-w-4xl">
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  1
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Gap Analysis & Planning</h3>
                  <p className="text-muted-foreground">
                    Comprehensive assessment of your current security posture against ISO 27001 requirements with detailed implementation roadmap.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  2
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">ISMS Setup & Configuration</h3>
                  <p className="text-muted-foreground">
                    Platform configuration, policy template deployment, and initial control implementation setup.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  3
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Control Implementation</h3>
                  <p className="text-muted-foreground">
                    Systematic implementation of security controls with automated monitoring and evidence collection.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  4
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Internal Audits & Review</h3>
                  <p className="text-muted-foreground">
                    Regular internal audits, management reviews, and continuous improvement processes.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  5
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Certification Support</h3>
                  <p className="text-muted-foreground">
                    Preparation for external audit, certification body coordination, and ongoing compliance maintenance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Why Choose Auris for ISO 27001?
            </h2>
          </div>
          
          <div className="mx-auto max-w-4xl">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary mt-1" />
                <div>
                  <h4 className="font-semibold mb-1">Faster Implementation</h4>
                  <p className="text-sm text-muted-foreground">Reduce implementation time by up to 60% with automated workflows and pre-built templates.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary mt-1" />
                <div>
                  <h4 className="font-semibold mb-1">Lower Costs</h4>
                  <p className="text-sm text-muted-foreground">Minimize consulting fees and internal resource requirements with guided implementation.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary mt-1" />
                <div>
                  <h4 className="font-semibold mb-1">Continuous Compliance</h4>
                  <p className="text-sm text-muted-foreground">Maintain compliance year-round with automated monitoring and reporting.</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-primary mt-1" />
                <div>
                  <h4 className="font-semibold mb-1">Expert Support</h4>
                  <p className="text-sm text-muted-foreground">Access to certified ISO 27001 lead auditors and implementation specialists.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
              Start Your ISO 27001 Journey
            </h2>
            <p className="mt-4 text-lg text-primary-foreground/80">
              See how Auris GRCOS can accelerate your ISO 27001 implementation and certification.
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/demo/request">
                  Book Demo
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10" asChild>
                <Link href="/contact">Contact Expert</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
