import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { ArrowRight, Eye, Shield, Activity, AlertTriangle } from "lucide-react";

export default function ExposureManagementPage() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Exposure <span className="text-primary">Management</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              Comprehensive visibility into your attack surface with continuous monitoring 
              and risk assessment across all digital assets and infrastructure.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Key Features
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Eye className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Asset Discovery</h3>
                <p className="text-muted-foreground">
                  Automatically discover and catalog all digital assets across your infrastructure, 
                  including cloud resources, on-premises systems, and third-party services.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Shield className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Risk Assessment</h3>
                <p className="text-muted-foreground">
                  Continuous risk assessment of exposed assets with prioritization based on 
                  business impact and threat intelligence.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <Activity className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Real-time Monitoring</h3>
                <p className="text-muted-foreground">
                  24/7 monitoring of your attack surface with instant alerts for new exposures 
                  and changes in risk posture.
                </p>
              </div>

              <div className="rounded-lg border bg-background p-6">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary mb-4">
                  <AlertTriangle className="h-6 w-6 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-4">Threat Intelligence</h3>
                <p className="text-muted-foreground">
                  Integration with threat intelligence feeds to identify assets under active 
                  attack or targeted by threat actors.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8">
              Benefits
            </h2>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Shield className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Reduce Attack Surface</h3>
                  <p className="text-muted-foreground">
                    Identify and eliminate unnecessary exposures to minimize your organization's attack surface.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Eye className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Complete Visibility</h3>
                  <p className="text-muted-foreground">
                    Gain comprehensive visibility into all digital assets and their security posture.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                  <Activity className="h-4 w-4 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">Proactive Security</h3>
                  <p className="text-muted-foreground">
                    Move from reactive to proactive security with continuous monitoring and early threat detection.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
