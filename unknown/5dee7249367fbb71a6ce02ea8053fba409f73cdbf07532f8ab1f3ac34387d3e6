import React from "react";
import { useId } from "react";

export default function FeaturesSectionDemo() {
  return (
    <div className="py-20 lg:py-40">
      <div
        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10 md:gap-2 max-w-7xl mx-auto">
        {grid.map((feature) => (
          <div
            key={feature.title}
            className="relative bg-gradient-to-b dark:from-neutral-900 from-neutral-100 dark:to-neutral-950 to-white p-6 rounded-3xl overflow-hidden">
            <Grid size={20} />
            <p
              className="text-base font-bold text-neutral-800 dark:text-white relative z-20">
              {feature.title}
            </p>
            <p
              className="text-neutral-600 dark:text-neutral-400 mt-4 text-base font-normal relative z-20">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

const grid = [
  {
    title: "HIPAA and SOC2 Compliant",
    description:
      "Our platform is HIPAA and SOC2 compliant, ensuring your sensitive data is protected with enterprise-grade security.",
  },
  {
    title: "ISO 27001 Automation",
    description:
      "Automate ISO 27001 compliance workflows with intelligent risk assessments and continuous monitoring capabilities.",
  },
  {
    title: "Real-time Risk Assessment",
    description:
      "Monitor and assess compliance risks in real-time with our advanced GRCOS platform and automated alerting system.",
  },
  {
    title: "Audit Trail Management",
    description:
      "Maintain comprehensive audit trails with automated documentation and evidence collection for seamless audits.",
  },
  {
    title: "Policy Management",
    description:
      "Centralize and manage all compliance policies with version control, approval workflows, and automated distribution.",
  },
  {
    title: "Compliance Dashboard",
    description:
      "Get a unified view of your compliance posture with interactive dashboards and detailed reporting capabilities.",
  },
  {
    title: "Expert Directory",
    description:
      "Access certified auditors and compliance experts through our curated directory for specialized guidance and support.",
  },
  {
    title: "Multi-framework Support",
    description:
      "Support for multiple compliance frameworks including PCI DSS, POPIA, GDPR, and more in a single platform.",
  },
];

export const Grid = ({
  pattern,
  size
}) => {
  const generateUniquePattern = () => {
    const coordinates = new Set();
    const result = [];
    
    while (result.length < 5) {
      const x = Math.floor(Math.random() * 4) + 7;
      const y = Math.floor(Math.random() * 6) + 1;
      const key = `${x}-${y}`;
      
      if (!coordinates.has(key)) {
        coordinates.add(key);
        result.push([x, y]);
      }
    }
    
    return result;
  };

  const p = pattern ?? generateUniquePattern();
  return (
    <div
      className="pointer-events-none absolute left-1/2 top-0  -ml-20 -mt-2 h-full w-full [mask-image:linear-gradient(white,transparent)]">
      <div
        className="absolute inset-0 bg-gradient-to-r  [mask-image:radial-gradient(farthest-side_at_top,white,transparent)] dark:from-zinc-900/30 from-zinc-100/30 to-zinc-300/30 dark:to-zinc-900/30 opacity-100">
        <GridPattern
          width={size ?? 20}
          height={size ?? 20}
          x="-12"
          y="4"
          squares={p}
          className="absolute inset-0 h-full w-full  mix-blend-overlay dark:fill-white/10 dark:stroke-white/10 stroke-black/10 fill-black/10" />
      </div>
    </div>
  );
};

export function GridPattern({
  width,
  height,
  x,
  y,
  squares,
  ...props
}) {
  const patternId = useId();

  return (
    <svg aria-hidden="true" {...props}>
      <defs>
        <pattern
          id={patternId}
          width={width}
          height={height}
          patternUnits="userSpaceOnUse"
          x={x}
          y={y}>
          <path d={`M.5 ${height}V.5H${width}`} fill="none" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" strokeWidth={0} fill={`url(#${patternId})`} />
      {squares && (
        <svg x={x} y={y} className="overflow-visible">
          {squares.map(([x, y]) => (
            <rect
              strokeWidth="0"
              key={`${x}-${y}`}
              width={width + 1}
              height={height + 1}
              x={x * width}
              y={y * height} />
          ))}
        </svg>
      )}
    </svg>
  );
}
