import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";
import {
  ArrowRight,
  Users,
  Building,
  Briefcase,
  Zap,
  CheckCircle,
  Clock,
  DollarSign,
  TrendingUp,
  Target,
  Lightbulb,
  BarChart3,
  Globe,
  Rocket,
  Shield,
  Bot,
  Workflow
} from "lucide-react";
import { notFound } from "next/navigation";

// Company size data
const sizeData = {
  "startups": {
    name: "Startups",
    title: "GRC for Startups & Small Businesses",
    description: "Enterprise-grade security without the enterprise complexity",
    longDescription: "Auris GRCOS delivers enterprise-grade GRC capabilities designed specifically for startups and small businesses. Get compliant faster, stay secure longer, and focus on what matters most—growing your business.",
    icon: Users,
    keyStats: [
      { value: "85%", label: "faster compliance implementation" },
      { value: "60%", label: "reduction in manual security tasks" },
      { value: "$200K", label: "average savings on traditional GRC solutions" },
      { value: "30+", label: "frameworks supported out-of-the-box" }
    ],
    challenges: [
      {
        title: "Resource constraints",
        description: "Limited budget and personnel for security initiatives",
        icon: DollarSign,
        color: "red"
      },
      {
        title: "Compliance complexity",
        description: "Multiple frameworks with overlapping but different requirements",
        icon: Target,
        color: "orange"
      },
      {
        title: "Investor requirements",
        description: "Due diligence demands robust security posture",
        icon: TrendingUp,
        color: "blue"
      },
      {
        title: "Customer trust",
        description: "B2B clients require security certifications and compliance proof",
        icon: Shield,
        color: "green"
      },
      {
        title: "Rapid growth",
        description: "Security processes that can't scale with business expansion",
        icon: Rocket,
        color: "purple"
      },
      {
        title: "Technical debt",
        description: "Security decisions made early that become costly later",
        icon: Clock,
        color: "yellow"
      }
    ],
    solutionBenefits: [
      {
        title: "AI-Driven Automation",
        description: "Automated risk assessments and remediation planning. Intelligent control mapping across multiple frameworks. Smart workflow orchestration reduces manual effort by 85%",
        icon: Bot,
        emoji: "🤖"
      },
      {
        title: "Unified Compliance Dashboard",
        description: "Single view across ISO 27001, SOC 2, PCI DSS, GDPR, and more. Real-time compliance scoring and gap analysis. Automated evidence collection and documentation",
        icon: BarChart3,
        emoji: "📊"
      },
      {
        title: "Rapid Implementation",
        description: "Get compliance-ready in weeks, not months. Pre-built templates for common startup scenarios. No dedicated security team required",
        icon: Zap,
        emoji: "⚡"
      },
      {
        title: "Startup-Friendly Pricing",
        description: "Fraction of traditional GRC solution costs. Scale pricing that grows with your business. No hidden fees or surprise charges",
        icon: DollarSign,
        emoji: "💰"
      }
    ],
    features: [
      "Quick setup and implementation",
      "Essential compliance frameworks",
      "Affordable pricing for early-stage companies",
      "Scalable as you grow"
    ],
    benefits: [
      "Investor-ready compliance posture",
      "Customer trust from day one",
      "Reduced time to market",
      "Foundation for future growth"
    ],
    testimonials: [
      {
        quote: "We went from zero compliance to SOC 2 ready in 6 weeks. GRCOS made it possible to compete for enterprise contracts without hiring a full security team.",
        name: "Sarah Chen",
        designation: "CTO at DataFlow",
        src: "/api/placeholder/400/400"
      },
      {
        quote: "GRCOS helped us demonstrate enterprise-grade security posture during our Series B. The automated reporting and risk management gave investors confidence in our approach.",
        name: "Michael Rodriguez",
        designation: "CEO at FinanceCore",
        src: "/api/placeholder/400/400"
      },
      {
        quote: "What used to take our team weeks of manual work now happens automatically. We can focus on product development while staying compliant across multiple frameworks.",
        name: "Jennifer Kim",
        designation: "Founder at MedTech Solutions",
        src: "/api/placeholder/400/400"
      }
    ],
    productModules: [
      {
        name: "LightHouse",
        subtitle: "Your Security Foundation",
        description: "Asset Management & Security Monitoring Made Simple",
        color: "blue",
        features: [
          "Asset Discovery: Automatically catalog all your IT assets",
          "Continuous Monitoring: 24/7 security monitoring without dedicated SOC",
          "Threat Intelligence: Stay ahead of threats relevant to your industry",
          "Risk Assessment: AI-powered risk analysis that prioritizes what matters"
        ]
      },
      {
        name: "ActionCentre",
        subtitle: "Automated Response",
        description: "Turn Security Events Into Automated Actions",
        color: "orange",
        features: [
          "Incident Response: Pre-built playbooks for common security incidents",
          "Vulnerability Management: Automated patch management and remediation",
          "Workflow Automation: Custom workflows that adapt to your processes",
          "Response Coordination: Streamlined incident response without chaos"
        ]
      },
      {
        name: "ComplianceCentre",
        subtitle: "Framework Mastery",
        description: "Navigate Multiple Compliance Requirements Effortlessly",
        color: "green",
        features: [
          "Multi-Framework Support: ISO 27001, SOC 2, GDPR, PCI DSS, and more",
          "Gap Analysis: Identify and prioritize compliance gaps",
          "Policy Management: AI-assisted policy creation and maintenance",
          "Assessment Automation: Scheduled assessments with minimal manual work"
        ]
      },
      {
        name: "TrustCentre",
        subtitle: "Evidence & Reporting",
        description: "Build Trust Through Transparency",
        color: "yellow",
        features: [
          "Automated Documentation: Generate compliance reports automatically",
          "Stakeholder Portals: Secure sharing with auditors and customers",
          "Evidence Management: Blockchain-secured compliance artifacts",
          "Audit Readiness: Always ready for customer or regulatory audits"
        ]
      }
    ],
    useCases: [
      {
        title: "Pre-Series A Startup",
        subtitle: "\"We need to show investors we take security seriously\"",
        challenge: "12-person SaaS startup preparing for Series A funding round. Investors are asking about security posture and compliance readiness.",
        solution: "GRCOS provides rapid ISO 27001 and SOC 2 foundation setup, automated risk assessment, and investor-ready security documentation in 4 weeks.",
        outcome: "Successfully raised $8M Series A with security posture as a competitive advantage."
      },
      {
        title: "B2B SaaS Growth Stage",
        subtitle: "\"Enterprise customers require security certifications\"",
        challenge: "45-person company losing enterprise deals due to lack of security certifications and compliance documentation.",
        solution: "Accelerated SOC 2 Type II preparation, automated evidence collection, and customer-facing trust portal implementation.",
        outcome: "Achieved SOC 2 Type II in 6 months, increased enterprise deal closure rate by 300%."
      },
      {
        title: "Regulated Industry Startup",
        subtitle: "\"We're in healthcare but can't afford a full compliance team\"",
        challenge: "Digital health startup needing HIPAA compliance with limited resources and regulatory expertise.",
        solution: "HIPAA-specific control implementation, automated risk assessments, and integrated third-party risk management.",
        outcome: "Achieved HIPAA compliance in 8 weeks, reduced compliance management time by 75%."
      }
    ],
    pricing: [
      {
        name: "Startup Plan",
        price: "$2,500/month",
        description: "Perfect for companies with 1-50 employees",
        features: [
          "Complete GRCOS platform access",
          "2 framework implementations (choose from ISO 27001, SOC 2, GDPR, PCI DSS)",
          "AI-powered risk assessments",
          "Automated workflow orchestration"
        ]
      },
      {
        name: "Growth Plan",
        price: "$5,000/month",
        description: "Ideal for scaling companies with 51-200 employees",
        features: [
          "Everything in Startup, plus:",
          "5 framework implementations",
          "Custom workflow development",
          "Dedicated customer success manager",
          "Priority support"
        ]
      },
      {
        name: "Custom Enterprise",
        price: "Contact us for pricing",
        description: "For companies requiring specialized compliance or high-volume processing",
        features: []
      }
    ]
  },
  "smes": {
    name: "SMEs",
    title: "Enterprise Security for Small & Medium Enterprises",
    description: "Professional compliance management without the enterprise overhead",
    longDescription: "Your SME operates with enterprise-level complexity but SME-level resources. GRCOS bridges that gap with sophisticated compliance automation that works within your operational constraints and regulatory requirements.",
    icon: Building,
    authorityIndicators: [
      "Trusted by 300+ regulated SMEs",
      "Supports multi-site, multi-jurisdiction compliance",
      "Average 40% reduction in compliance costs",
      "Built for regulated industries"
    ],
    challenges: [
      {
        title: "Regulatory scrutiny",
        description: "Subject to the same compliance requirements as larger competitors",
        icon: Shield,
        color: "red"
      },
      {
        title: "Customer due diligence",
        description: "B2B clients conduct thorough security assessments",
        icon: Users,
        color: "orange"
      },
      {
        title: "Multi-framework complexity",
        description: "Often need compliance across 3-5 different standards simultaneously",
        icon: Target,
        color: "blue"
      },
      {
        title: "Resource constraints",
        description: "Limited dedicated security or compliance personnel",
        icon: DollarSign,
        color: "green"
      },
      {
        title: "Geographic complexity",
        description: "Operating across regions with different regulatory requirements",
        icon: Globe,
        color: "purple"
      },
      {
        title: "Audit frequency",
        description: "Regular external audits with high stakes for certification maintenance",
        icon: Clock,
        color: "yellow"
      }
    ],
    solutionBenefits: [
      {
        title: "Multi-framework orchestration",
        description: "Manage ISO 27001, SOC 2, PCI DSS, GDPR, and industry-specific requirements from one platform",
        icon: Workflow,
        emoji: "🔄"
      },
      {
        title: "Mature workflow automation",
        description: "Handle complex compliance processes that span departments and locations",
        icon: Bot,
        emoji: "⚙️"
      },
      {
        title: "Regulatory intelligence",
        description: "Stay ahead of changing requirements across jurisdictions",
        icon: Lightbulb,
        emoji: "💡"
      },
      {
        title: "Stakeholder management",
        description: "Coordinate between internal teams, external auditors, and regulatory bodies",
        icon: Users,
        emoji: "👥"
      }
    ],
    features: [
      "Multi-framework support",
      "Automated compliance workflows",
      "Team collaboration tools",
      "Regular compliance assessments"
    ],
    benefits: [
      "Competitive advantage through compliance",
      "Reduced compliance overhead",
      "Better risk management",
      "Preparation for enterprise growth"
    ],
    productModules: [
      {
        name: "LightHouse",
        subtitle: "Enterprise Asset & Risk Management",
        description: "Complete visibility and control across your organization",
        color: "blue",
        features: [
          "Multi-site asset management: Unified inventory across locations and subsidiaries",
          "Advanced threat detection: 24/7 monitoring with AI-powered anomaly detection",
          "Risk correlation: Understand how risks compound across business units",
          "Compliance mapping: Automatic control testing across multiple frameworks"
        ]
      },
      {
        name: "ActionCentre",
        subtitle: "Mature Process Automation",
        description: "Sophisticated workflows that scale across departments",
        color: "orange",
        features: [
          "Cross-functional workflows: Coordinate compliance activities across teams",
          "Regulatory response automation: Structured responses to regulatory inquiries",
          "Vendor risk management: Automated third-party risk assessments",
          "Incident coordination: Enterprise-grade incident response without dedicated CSIRT"
        ]
      },
      {
        name: "ComplianceCentre",
        subtitle: "Multi-Framework Mastery",
        description: "Simultaneous management of complex compliance requirements",
        color: "green",
        features: [
          "Framework harmonization: Optimize overlapping controls across standards",
          "Regulatory change tracking: Stay current with evolving requirements",
          "Multi-jurisdiction support: Handle different regulatory requirements by region",
          "Maturity assessment: Benchmark and improve your compliance program over time"
        ]
      },
      {
        name: "TrustCentre",
        subtitle: "Stakeholder Confidence",
        description: "Professional documentation and reporting for all stakeholders",
        color: "yellow",
        features: [
          "Audit management: Streamlined coordination with external auditors",
          "Executive reporting: Board-level risk and compliance dashboards",
          "Customer assurance: Professional trust portals for due diligence requests",
          "Regulatory reporting: Automated compliance reporting for various authorities"
        ]
      }
    ],
    useCases: [
      {
        title: "Regional Financial Services Firm",
        subtitle: "125 employees, 4 locations",
        challenge: "Required SOC 2, PCI DSS, and regional banking regulations compliance",
        solution: "Unified multi-framework implementation with centralized monitoring",
        outcome: "Reduced compliance management time by 55%, passed all audits first time, expanded into two new markets"
      },
      {
        title: "Manufacturing Company",
        subtitle: "200 employees, international operations",
        challenge: "ISO 27001, GDPR, and country-specific data protection requirements",
        solution: "Centralized compliance management with location-specific customization",
        outcome: "Achieved ISO 27001 certification 4 months ahead of schedule, reduced compliance costs by $180K annually"
      },
      {
        title: "Healthcare Services Provider",
        subtitle: "85 employees, multi-state",
        challenge: "HIPAA, state healthcare regulations, and SOC 2 for technology services",
        solution: "Integrated compliance across clinical and technology operations",
        outcome: "Streamlined compliance across all business units, enabled expansion into 3 new states"
      },
      {
        title: "Professional Services Firm",
        subtitle: "150 employees, global clients",
        challenge: "Multiple client-required certifications and international data protection laws",
        solution: "Flexible framework management supporting client-specific requirements",
        outcome: "Increased enterprise client base by 40%, reduced proposal response time by 60%"
      }
    ],
    pricing: [
      {
        name: "Professional",
        price: "$5,999/month",
        description: "For established SMEs with formal compliance requirements",
        subtitle: "Designed for organizations that need multiple compliance framework management, multi-location coordination, and professional audit support",
        features: [
          "Complete platform access for up to 200 users",
          "Up to 5 compliance framework implementations",
          "Advanced workflow automation and orchestration",
          "Priority support with dedicated customer success manager",
          "Professional services for initial setup and training"
        ]
      },
      {
        name: "Enterprise SME",
        price: "$9,999/month",
        description: "For complex SMEs with sophisticated requirements",
        subtitle: "Perfect for organizations with high-regulation industry requirements and international operations",
        features: [
          "Everything in Professional, plus:",
          "Unlimited framework implementations",
          "Custom integration development",
          "Advanced threat intelligence and response capabilities",
          "White-glove audit support",
          "Regulatory change management services"
        ]
      },
      {
        name: "Regulated Industry Packages",
        price: "Custom pricing",
        description: "Specialized solutions for healthcare, financial services, government contractors",
        subtitle: "",
        features: []
      }
    ],
    testimonials: [
      {
        quote: "We operate across three countries with different regulatory requirements. GRCOS gave us centralized control without losing the flexibility to meet local compliance needs. Our audit prep time went from 8 weeks to 2 weeks.",
        name: "James Morrison",
        designation: "Compliance Director at TechServices Ltd",
        src: "/api/placeholder/400/400"
      },
      {
        quote: "We needed enterprise-level risk management but couldn't justify hiring a full compliance team. GRCOS gives us the capabilities of a much larger organization at a fraction of the cost.",
        name: "Maria Santos",
        designation: "Managing Director at Regional Bank",
        src: "/api/placeholder/400/400"
      },
      {
        quote: "Board meetings are completely different now. Instead of explaining compliance problems, we're discussing strategic opportunities. GRCOS transformed compliance from a cost center to a competitive advantage.",
        name: "David Chen",
        designation: "IT Director at Manufacturing Plus",
        src: "/api/placeholder/400/400"
      }
    ]
  },
  "enterprise": {
    name: "Enterprise",
    title: "Enterprise Compliance Management",
    description: "Large organization compliance management with advanced features.",
    longDescription: "Comprehensive compliance management for large organizations with complex requirements, multiple frameworks, and global operations.",
    icon: Briefcase,
    features: [
      "Advanced workflow automation",
      "Multi-tenant architecture",
      "Global compliance support",
      "Enterprise integrations"
    ],
    benefits: [
      "Centralized compliance management",
      "Reduced compliance costs",
      "Enhanced audit readiness",
      "Global regulatory coverage"
    ]
  }
};

export default async function SizePage({ params }) {
  const resolvedParams = await params;
  const size = sizeData[resolvedParams.size];

  if (!size) {
    notFound();
  }

  const Icon = size.icon;

  // Special handling for startups and SMEs pages with comprehensive content
  if (resolvedParams.size === "startups") {
    return (
      <div className="flex flex-col">
        {/* Hero Section */}
        <section className="relative py-20 lg:py-32">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl text-center">
              <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
                {size.title}
              </h1>
              <p className="mt-4 text-xl text-muted-foreground">
                {size.description}
              </p>
              <h2 className="mt-8 text-2xl font-semibold">
                Scale securely from day one
              </h2>
              <p className="mt-4 text-lg leading-8 text-muted-foreground">
                Build trust with customers and investors through automated compliance that grows with your business
              </p>
              <p className="mt-6 text-lg leading-8 text-muted-foreground">
                {size.longDescription}
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button size="lg" asChild>
                  <Link href="/demo">
                    See Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/waitlist">Join Waitlist</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Key Stats Bar */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
              {size.keyStats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Problem Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  The Startup Security Dilemma
                </h2>
                <p className="text-xl text-muted-foreground">
                  You need enterprise security. You don&apos;t have enterprise resources.
                </p>
              </div>

              <div className="mb-12">
                <p className="text-lg text-muted-foreground mb-8">
                  Growing startups face an impossible choice: invest heavily in complex security infrastructure or risk losing customers,
                  funding, and competitive advantage. Traditional GRC solutions cost hundreds of thousands of dollars and require
                  dedicated security teams you can&apos;t afford to hire.
                </p>
              </div>

              <div>
                <h3 className="text-2xl font-semibold mb-8">Common Challenges:</h3>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {size.challenges.map((challenge, index) => {
                    const ChallengeIcon = challenge.icon;
                    return (
                      <div key={index} className="flex items-start space-x-4">
                        <div className={`flex h-12 w-12 items-center justify-center rounded-lg bg-${challenge.color}-100 dark:bg-${challenge.color}-900/20`}>
                          <ChallengeIcon className={`h-6 w-6 text-${challenge.color}-600 dark:text-${challenge.color}-400`} />
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">{challenge.title}</h4>
                          <p className="text-muted-foreground">{challenge.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Solution Section */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  AI-Powered GRC That Scales With You
                </h2>
                <p className="text-lg text-muted-foreground">
                  GRCOS transforms enterprise-grade security into a startup-friendly solution through intelligent automation and unified compliance management.
                </p>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {size.solutionBenefits.map((benefit, index) => (
                    <div key={index} className="rounded-lg border bg-background p-8">
                      <div className="flex items-center mb-6">
                        <div className="text-2xl mr-3">{benefit.emoji}</div>
                        <div>
                          <h3 className="text-xl font-semibold">{benefit.title}</h3>
                        </div>
                      </div>
                      <p className="text-muted-foreground">{benefit.description}</p>
                    </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Product Modules */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Product Modules for Startups
                </h2>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {size.productModules.map((module, index) => (
                  <div key={index} className="rounded-lg border bg-background p-8">
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold text-primary mb-2">{module.name}: {module.subtitle}</h3>
                      <p className="text-muted-foreground">{module.description}</p>
                    </div>
                    <ul className="space-y-3">
                      {module.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start space-x-3">
                          <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Use Cases */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Use Cases
                </h2>
              </div>

              <div className="space-y-12">
                {size.useCases.map((useCase, index) => (
                  <div key={index} className="rounded-lg border bg-background p-8">
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold mb-2">Scenario {index + 1}: {useCase.title}</h3>
                      <p className="text-lg text-primary italic">{useCase.subtitle}</p>
                    </div>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div>
                        <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                        <p className="text-sm text-muted-foreground">{useCase.challenge}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-blue-600">Solution:</h4>
                        <p className="text-sm text-muted-foreground">{useCase.solution}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-green-600">Outcome:</h4>
                        <p className="text-sm text-muted-foreground">{useCase.outcome}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Pricing */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Pricing for Startups
                </h2>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                {size.pricing.map((plan, index) => (
                  <div key={index} className={`rounded-lg border p-8 ${index === 1 ? 'border-primary bg-primary/5' : 'bg-background'}`}>
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold mb-2">{plan.name}</h3>
                      <div className="text-2xl font-bold text-primary mb-2">{plan.price}</div>
                      <p className="text-sm text-muted-foreground">{plan.description}</p>
                    </div>
                    {plan.features.length > 0 && (
                      <ul className="space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start space-x-3">
                            <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Why Startups Choose GRCOS
                </h2>
                <p className="text-lg text-muted-foreground">
                  &ldquo;Finally, enterprise security we can actually afford&rdquo;
                </p>
              </div>

              <AnimatedTestimonials testimonials={size.testimonials} autoplay={true} />
            </div>
          </div>
        </section>

        {/* Getting Started */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Ready to Scale Securely?
              </h2>
              <p className="text-lg text-muted-foreground mb-12">
                Questions? Our startup specialists understand the unique challenges of growing companies. We&apos;re here to help you build security that scales.
              </p>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <Button size="lg" asChild>
                  <Link href="/demo">
                    Book a Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/waitlist">Start Free Trial</Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">Contact Sales</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  // Special handling for SMEs page with comprehensive content
  if (resolvedParams.size === "smes") {
    return (
      <div className="flex flex-col">
        {/* Hero Section */}
        <section className="relative py-20 lg:py-32">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl text-center">
              <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
                {size.title}
              </h1>
              <p className="mt-4 text-xl text-muted-foreground italic">
                {size.description}
              </p>
              <h2 className="mt-8 text-2xl font-semibold">
                Mature security operations that match your business ambitions
              </h2>
              <p className="mt-4 text-lg leading-8 text-muted-foreground">
                Get the compliance capabilities and risk management maturity your stakeholders expect—built for SME realities and budgets.
              </p>
              <p className="mt-6 text-lg leading-8 text-muted-foreground">
                {size.longDescription}
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <Button size="lg" asChild>
                  <Link href="/demo">
                    See Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/waitlist">Join Waitlist</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Authority Indicators */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 max-w-6xl mx-auto">
              {size.authorityIndicators.map((indicator, index) => (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-3">
                    <CheckCircle className="h-6 w-6 text-primary" />
                  </div>
                  <div className="text-sm text-muted-foreground">{indicator}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Problem Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  The SME Compliance Challenge
                </h2>
                <p className="text-xl text-muted-foreground mb-8">
                  You&apos;re caught between two worlds:
                </p>
                <h3 className="text-2xl font-semibold mb-4">
                  Enterprise expectations with SME resources
                </h3>
              </div>

              <div className="mb-12">
                <p className="text-lg text-muted-foreground mb-8">
                  Your customers, regulators, and stakeholders expect enterprise-grade security and compliance. But you don&apos;t have enterprise budgets,
                  dedicated compliance teams, or the luxury of 18-month implementation projects.
                </p>
              </div>

              <div className="mb-12">
                <h3 className="text-2xl font-semibold mb-8">The specific pressures SMEs face:</h3>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {size.challenges.map((challenge, index) => {
                    const ChallengeIcon = challenge.icon;
                    return (
                      <div key={index} className="flex items-start space-x-4">
                        <div className={`flex h-12 w-12 items-center justify-center rounded-lg bg-${challenge.color}-100 dark:bg-${challenge.color}-900/20`}>
                          <ChallengeIcon className={`h-6 w-6 text-${challenge.color}-600 dark:text-${challenge.color}-400`} />
                        </div>
                        <div>
                          <h4 className="font-semibold mb-2">{challenge.title}</h4>
                          <p className="text-muted-foreground">{challenge.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="rounded-lg border bg-red-50 dark:bg-red-900/10 p-8">
                <h4 className="text-xl font-semibold mb-4 text-red-800 dark:text-red-200">The cost of inadequate compliance:</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Lost major contracts (average SME loses $5.2M annually to compliance gaps)</li>
                  <li>• Regulatory fines and enforcement actions</li>
                  <li>• Expensive emergency remediation projects</li>
                  <li>• Reputation damage in regulated industries</li>
                  <li>• Inability to expand into new markets or regions</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Solution Section */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  How GRCOS Solves SME Compliance
                </h2>
                <p className="text-lg text-muted-foreground">
                  Enterprise capabilities without enterprise complexity or cost
                </p>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {size.solutionBenefits.map((benefit, index) => (
                  <div key={index} className="rounded-lg border bg-background p-8">
                    <div className="flex items-center mb-6">
                      <div className="text-2xl mr-3">{benefit.emoji}</div>
                      <div>
                        <h3 className="text-xl font-semibold">{benefit.title}</h3>
                      </div>
                    </div>
                    <p className="text-muted-foreground">{benefit.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Product Modules */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Comprehensive SME Solution Set
                </h2>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                {size.productModules.map((module, index) => (
                  <div key={index} className="rounded-lg border bg-background p-8">
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold text-primary mb-2">{module.name}: {module.subtitle}</h3>
                      <p className="text-muted-foreground">{module.description}</p>
                    </div>
                    <ul className="space-y-3">
                      {module.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start space-x-3">
                          <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Use Cases */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Proven SME Success Stories
                </h2>
              </div>

              <div className="space-y-12">
                {size.useCases.map((useCase, index) => (
                  <div key={index} className="rounded-lg border bg-background p-8">
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold mb-2">{useCase.title}</h3>
                      <p className="text-lg text-primary italic">{useCase.subtitle}</p>
                    </div>
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div>
                        <h4 className="font-semibold mb-2 text-red-600">Challenge:</h4>
                        <p className="text-sm text-muted-foreground">{useCase.challenge}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-blue-600">Approach:</h4>
                        <p className="text-sm text-muted-foreground">{useCase.solution}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-green-600">Result:</h4>
                        <p className="text-sm text-muted-foreground">{useCase.outcome}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Pricing */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  SME-Focused Pricing
                </h2>
              </div>

              <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                {size.pricing.map((plan, index) => (
                  <div key={index} className={`rounded-lg border p-8 ${index === 0 ? 'border-primary bg-primary/5' : 'bg-background'}`}>
                    <div className="mb-6">
                      <h3 className="text-xl font-semibold mb-2">{plan.name}</h3>
                      <div className="text-2xl font-bold text-primary mb-2">{plan.price}</div>
                      <p className="text-sm text-muted-foreground mb-2">{plan.description}</p>
                      {plan.subtitle && <p className="text-xs text-muted-foreground">{plan.subtitle}</p>}
                    </div>
                    {plan.features.length > 0 && (
                      <ul className="space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start space-x-3">
                            <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Why SMEs Choose GRCOS
                </h2>
                <p className="text-lg text-muted-foreground">
                  &ldquo;Finally, compliance management that understands our business model&rdquo;
                </p>
              </div>

              <AnimatedTestimonials testimonials={size.testimonials} autoplay={true} />
            </div>
          </div>
        </section>

        {/* Getting Started */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="mx-auto max-w-4xl text-center">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                Getting Started
              </h2>
              <p className="text-lg text-muted-foreground mb-12">
                Ready to elevate your compliance program? Most SME implementations show measurable ROI within the first quarter.
              </p>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <Button size="lg" asChild>
                  <Link href="/demo">
                    Assessment & Strategy Session
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/waitlist">30-Day Pilot Program</Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/contact">Executive Briefing</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  // Default handling for Enterprise
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-lg bg-primary mb-8">
              <Icon className="h-10 w-10 text-primary-foreground" />
            </div>
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              <span className="text-primary">{size.name}</span> Compliance Solutions
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
              {size.longDescription}
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">See Demo</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Features for {size.name}
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Compliance features tailored to the needs of {size.name.toLowerCase()}.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {size.features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4 p-6 rounded-lg border bg-background">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  {index + 1}
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{feature}</h3>
                  <p className="text-muted-foreground">
                    Designed specifically to meet the compliance needs of {size.name.toLowerCase()}.
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center mb-16">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Benefits for {size.name}
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Why {size.name.toLowerCase()} choose Auris GRCOS for their compliance needs.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {size.benefits.map((benefit, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary">
                  <Zap className="h-6 w-6 text-primary-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{benefit}</h3>
                  <p className="text-muted-foreground">
                    Key advantage that {size.name.toLowerCase()} gain from using our compliance platform.
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Hint */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Pricing for {size.name}
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              {size.name === "Startups" && "Affordable pricing designed for early-stage companies with limited budgets."}
              {size.name === "SMBs" && "Competitive pricing that scales with your business growth and compliance needs."}
              {size.name === "Enterprise" && "Enterprise pricing with volume discounts and custom packages available."}
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist for Early Pricing
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Coming Soon */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Detailed Solutions Coming Soon
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Comprehensive documentation and pricing for {size.name.toLowerCase()} solutions
              will be available when we launch.
            </p>
            <div className="mt-8">
              <Button size="lg" asChild>
                <Link href="/waitlist">
                  Join Waitlist
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export async function generateStaticParams() {
  return Object.keys(sizeData).map((size) => ({
    size: size,
  }));
}
